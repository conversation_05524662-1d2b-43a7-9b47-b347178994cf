// Moonshot AI API服务类 - 对话接口管理
// 遵循熨烫主题命名和差异化规则

import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import '../FabricVault/irx_chat_record_entity.dart';
import '../FabricVault/irx_ai_expert_entity.dart';

class IrxMoonshotApiService {
  static const String _baseUrl = 'https://api.moonshot.cn/v1';
  static const String _apiKey = 'sk-UjLDXgyVsdh4kNtrLbbu1yFFhuafggJTxEw03ezpnnvXX2fR';
  static const String _model = 'moonshot-v1-8k';
  
  // 单例模式
  static final IrxMoonshotApiService _instance = IrxMoonshotApiService._internal();
  factory IrxMoonshotApiService() => _instance;
  IrxMoonshotApiService._internal();

  // HTTP客户端
  final http.Client _client = http.Client();

  // 发送对话请求
  Future<String> sendConversation({
    required IrxAiExpertEntity expert,
    required List<IrxChatRecordEntity> conversationHistory,
    required String userMessage,
  }) async {
    try {
      // 构建系统提示词
      final systemPrompt = _buildSystemPrompt(expert);
      
      // 构建消息列表
      final messages = _buildMessageList(
        systemPrompt: systemPrompt,
        conversationHistory: conversationHistory,
        userMessage: userMessage,
      );

      // 构建请求体
      final requestBody = {
        'model': _model,
        'messages': messages,
        'temperature': 0.3,
      };

      // 发送HTTP请求
      final response = await _client.post(
        Uri.parse('$_baseUrl/chat/completions'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $_apiKey',
        },
        body: json.encode(requestBody),
      );

      // 处理响应
      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        final aiReply = responseData['choices'][0]['message']['content'] as String;
        return aiReply.trim();
      } else {
        throw IrxApiException(
          'API request failed: ${response.statusCode}',
          response.statusCode,
          response.body,
        );
      }
    } on SocketException {
      throw IrxApiException('Network connection failed, please check network settings', 0, '');
    } on FormatException {
      throw IrxApiException('Response data format error', 0, '');
    } catch (e) {
      if (e is IrxApiException) rethrow;
      throw IrxApiException('Unknown error: $e', 0, '');
    }
  }

  // 构建系统提示词
  String _buildSystemPrompt(IrxAiExpertEntity expert) {
    return '''You are ${expert.displayName}, a professional ironing guidance expert. Your specialty is ${expert.category}.

Important requirements:
- You must answer all questions in English only, absolutely no Chinese allowed
- Keep responses concise and clear, within 100 words
- Maintain a friendly, patient, and professional tone

Your characteristics:
- Professional knowledge: ${expert.longDesc}
- Response style: Friendly, patient, professional, explaining complex ironing techniques in simple English
- Safety first: Always emphasize ironing safety to avoid burns and fabric damage
- Practical focus: Provide specific actionable advice rather than theoretical knowledge

Please strictly answer user questions in English, maintaining a professional but friendly tone. If user questions are outside the ironing scope, please politely guide them back to ironing topics in English.''';
  }

  // 构建消息列表
  List<Map<String, String>> _buildMessageList({
    required String systemPrompt,
    required List<IrxChatRecordEntity> conversationHistory,
    required String userMessage,
  }) {
    final messages = <Map<String, String>>[];
    
    // 添加系统提示词
    messages.add({
      'role': 'system',
      'content': systemPrompt,
    });

    // 添加历史对话（最近10条）
    final recentHistory = conversationHistory.length > 10 
        ? conversationHistory.sublist(conversationHistory.length - 10)
        : conversationHistory;

    for (final record in recentHistory) {
      if (record.isUserMessage) {
        messages.add({
          'role': 'user',
          'content': record.content,
        });
      } else if (record.isAiMessage) {
        messages.add({
          'role': 'assistant',
          'content': record.content,
        });
      }
    }

    // 添加当前用户消息
    messages.add({
      'role': 'user',
      'content': userMessage,
    });

    return messages;
  }

  // 释放资源
  void dispose() {
    _client.close();
  }
}

// API异常类
class IrxApiException implements Exception {
  final String message;
  final int statusCode;
  final String responseBody;

  const IrxApiException(this.message, this.statusCode, this.responseBody);

  @override
  String toString() => 'IrxApiException: $message';

  // 获取用户友好的错误信息
  String get userFriendlyMessage {
    switch (statusCode) {
      case 401:
        return 'Invalid API key, please contact developer';
      case 429:
        return 'API call rate too high, please try again later';
      case 500:
        return 'AI service temporarily unavailable, please try again later';
      case 0:
        return message; // Network errors etc.
      default:
        return 'Service temporarily unavailable, please try again later';
    }
  }
}
