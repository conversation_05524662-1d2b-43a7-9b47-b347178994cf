// Moonshot AI API服务类 - 对话接口管理
// 遵循熨烫主题命名和差异化规则

import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import '../FabricVault/irx_chat_record_entity.dart';
import '../FabricVault/irx_ai_expert_entity.dart';

class IrxMoonshotApiService {
  static const String _baseUrl = 'https://api.moonshot.cn/v1';
  static const String _apiKey = 'sk-UjLDXgyVsdh4kNtrLbbu1yFFhuafggJTxEw03ezpnnvXX2fR';
  static const String _model = 'moonshot-v1-8k';
  
  // 单例模式
  static final IrxMoonshotApiService _instance = IrxMoonshotApiService._internal();
  factory IrxMoonshotApiService() => _instance;
  IrxMoonshotApiService._internal();

  // HTTP客户端
  final http.Client _client = http.Client();

  // 发送对话请求
  Future<String> sendConversation({
    required IrxAiExpertEntity expert,
    required List<IrxChatRecordEntity> conversationHistory,
    required String userMessage,
  }) async {
    try {
      // 构建系统提示词
      final systemPrompt = _buildSystemPrompt(expert);
      
      // 构建消息列表
      final messages = _buildMessageList(
        systemPrompt: systemPrompt,
        conversationHistory: conversationHistory,
        userMessage: userMessage,
      );

      // 构建请求体
      final requestBody = {
        'model': _model,
        'messages': messages,
        'temperature': 0.3,
      };

      // 发送HTTP请求
      final response = await _client.post(
        Uri.parse('$_baseUrl/chat/completions'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $_apiKey',
        },
        body: json.encode(requestBody),
      );

      // 处理响应
      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        final aiReply = responseData['choices'][0]['message']['content'] as String;
        return aiReply.trim();
      } else {
        throw IrxApiException(
          'API请求失败: ${response.statusCode}',
          response.statusCode,
          response.body,
        );
      }
    } on SocketException {
      throw IrxApiException('网络连接失败，请检查网络设置', 0, '');
    } on FormatException {
      throw IrxApiException('响应数据格式错误', 0, '');
    } catch (e) {
      if (e is IrxApiException) rethrow;
      throw IrxApiException('未知错误: $e', 0, '');
    }
  }

  // 构建系统提示词
  String _buildSystemPrompt(IrxAiExpertEntity expert) {
    return '''你是${expert.displayName}，一位专业的熨烫指导专家。你的专长是${expert.category}。

你的角色特点：
- 专业知识：${expert.longDesc}
- 回答风格：友好、耐心、专业，用简单易懂的语言解释复杂的熨烫技巧
- 安全第一：始终强调熨烫安全，避免烫伤和损坏衣物
- 实用导向：提供具体可操作的建议，而不是理论知识

请用中文回答用户的问题，保持专业但亲切的语调。如果用户问题超出熨烫范围，请礼貌地引导回到熨烫话题。''';
  }

  // 构建消息列表
  List<Map<String, String>> _buildMessageList({
    required String systemPrompt,
    required List<IrxChatRecordEntity> conversationHistory,
    required String userMessage,
  }) {
    final messages = <Map<String, String>>[];
    
    // 添加系统提示词
    messages.add({
      'role': 'system',
      'content': systemPrompt,
    });

    // 添加历史对话（最近10条）
    final recentHistory = conversationHistory.length > 10 
        ? conversationHistory.sublist(conversationHistory.length - 10)
        : conversationHistory;

    for (final record in recentHistory) {
      if (record.isUserMessage) {
        messages.add({
          'role': 'user',
          'content': record.content,
        });
      } else if (record.isAiMessage) {
        messages.add({
          'role': 'assistant',
          'content': record.content,
        });
      }
    }

    // 添加当前用户消息
    messages.add({
      'role': 'user',
      'content': userMessage,
    });

    return messages;
  }

  // 释放资源
  void dispose() {
    _client.close();
  }
}

// API异常类
class IrxApiException implements Exception {
  final String message;
  final int statusCode;
  final String responseBody;

  const IrxApiException(this.message, this.statusCode, this.responseBody);

  @override
  String toString() => 'IrxApiException: $message';

  // 获取用户友好的错误信息
  String get userFriendlyMessage {
    switch (statusCode) {
      case 401:
        return 'API密钥无效，请联系开发者';
      case 429:
        return 'API调用频率过高，请稍后再试';
      case 500:
        return 'AI服务暂时不可用，请稍后再试';
      case 0:
        return message; // 网络错误等
      default:
        return '服务暂时不可用，请稍后再试';
    }
  }
}
