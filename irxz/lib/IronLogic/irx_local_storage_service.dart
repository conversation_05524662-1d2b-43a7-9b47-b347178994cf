// 本地存储服务类 - 数据持久化管理
// 遵循熨烫主题命名和差异化规则

import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../FabricVault/irx_chat_record_entity.dart';
import '../FabricVault/irx_ai_expert_entity.dart';
import '../FabricVault/irx_user_profile_entity.dart';

class IrxLocalStorageService {
  // 单例模式
  static final IrxLocalStorageService _instance = IrxLocalStorageService._internal();
  factory IrxLocalStorageService() => _instance;
  IrxLocalStorageService._internal();

  // SharedPreferences实例
  SharedPreferences? _prefs;

  // 存储键名常量
  static const String _keyConversationRecords = 'irx_conversation_records';
  static const String _keyExpertUsageData = 'irx_expert_usage_data';
  static const String _keyUserProfileData = 'irx_user_profile_data';
  static const String _keyAppSettings = 'irx_app_settings';

  // 初始化
  Future<void> initialize() async {
    _prefs ??= await SharedPreferences.getInstance();
  }

  // 确保已初始化
  void _ensureInitialized() {
    if (_prefs == null) {
      throw Exception('LocalStorageService not initialized. Call initialize() first.');
    }
  }

  // ==================== 对话记录管理 ====================

  // 保存对话记录
  Future<bool> saveConversationRecord(IrxChatRecordEntity record) async {
    _ensureInitialized();
    
    try {
      final existingRecords = await getConversationRecords(record.expertId);
      existingRecords.add(record);
      
      final recordsJson = existingRecords.map((r) => r.toJson()).toList();
      final key = '${_keyConversationRecords}_${record.expertId}';
      
      return await _prefs!.setString(key, json.encode(recordsJson));
    } catch (e) {
      print('保存对话记录失败: $e');
      return false;
    }
  }

  // 批量保存对话记录
  Future<bool> saveConversationRecords(String expertId, List<IrxChatRecordEntity> records) async {
    _ensureInitialized();
    
    try {
      final recordsJson = records.map((r) => r.toJson()).toList();
      final key = '${_keyConversationRecords}_$expertId';
      
      return await _prefs!.setString(key, json.encode(recordsJson));
    } catch (e) {
      print('批量保存对话记录失败: $e');
      return false;
    }
  }

  // 获取对话记录
  Future<List<IrxChatRecordEntity>> getConversationRecords(String expertId) async {
    _ensureInitialized();
    
    try {
      final key = '${_keyConversationRecords}_$expertId';
      final recordsString = _prefs!.getString(key);
      
      if (recordsString == null || recordsString.isEmpty) {
        return [];
      }
      
      final recordsJson = json.decode(recordsString) as List;
      return recordsJson
          .map((json) => IrxChatRecordEntity.fromJson(json as Map<String, dynamic>))
          .toList();
    } catch (e) {
      print('获取对话记录失败: $e');
      return [];
    }
  }

  // 获取所有专家的对话记录摘要
  Future<Map<String, List<IrxChatRecordEntity>>> getAllConversationSummaries() async {
    _ensureInitialized();
    
    try {
      final allKeys = _prefs!.getKeys();
      final conversationKeys = allKeys
          .where((key) => key.startsWith(_keyConversationRecords))
          .toList();
      
      final summaries = <String, List<IrxChatRecordEntity>>{};
      
      for (final key in conversationKeys) {
        final expertId = key.replaceFirst('${_keyConversationRecords}_', '');
        final records = await getConversationRecords(expertId);
        
        if (records.isNotEmpty) {
          // 只保留最近3条记录作为摘要
          final recentRecords = records.length > 3 
              ? records.sublist(records.length - 3)
              : records;
          summaries[expertId] = recentRecords;
        }
      }
      
      return summaries;
    } catch (e) {
      print('获取对话摘要失败: $e');
      return {};
    }
  }

  // 清除特定专家的对话记录
  Future<bool> clearConversationRecords(String expertId) async {
    _ensureInitialized();
    
    try {
      final key = '${_keyConversationRecords}_$expertId';
      return await _prefs!.remove(key);
    } catch (e) {
      print('清除对话记录失败: $e');
      return false;
    }
  }

  // ==================== 专家使用数据管理 ====================

  // 保存专家使用数据
  Future<bool> saveExpertUsageData(List<IrxAiExpertEntity> experts) async {
    _ensureInitialized();
    
    try {
      final expertsJson = experts.map((e) => e.toJson()).toList();
      return await _prefs!.setString(_keyExpertUsageData, json.encode(expertsJson));
    } catch (e) {
      print('保存专家使用数据失败: $e');
      return false;
    }
  }

  // 获取专家使用数据
  Future<List<IrxAiExpertEntity>> getExpertUsageData() async {
    _ensureInitialized();
    
    try {
      final expertsString = _prefs!.getString(_keyExpertUsageData);
      
      if (expertsString == null || expertsString.isEmpty) {
        // 返回默认专家数据
        return IrxAiExpertEntity.createDefaultExperts();
      }
      
      final expertsJson = json.decode(expertsString) as List;
      return expertsJson
          .map((json) => IrxAiExpertEntity.fromJson(json as Map<String, dynamic>))
          .toList();
    } catch (e) {
      print('获取专家使用数据失败: $e');
      // 返回默认专家数据
      return IrxAiExpertEntity.createDefaultExperts();
    }
  }

  // 更新专家使用次数
  Future<bool> updateExpertUsageCount(String expertId) async {
    _ensureInitialized();
    
    try {
      final experts = await getExpertUsageData();
      final expertIndex = experts.indexWhere((e) => e.id == expertId);
      
      if (expertIndex != -1) {
        final updatedExpert = experts[expertIndex].copyWith(
          usedCount: experts[expertIndex].usedCount + 1,
          updatedAt: DateTime.now(),
        );
        experts[expertIndex] = updatedExpert;
        
        return await saveExpertUsageData(experts);
      }
      
      return false;
    } catch (e) {
      print('更新专家使用次数失败: $e');
      return false;
    }
  }

  // ==================== 用户资料管理 ====================

  // 保存用户资料
  Future<bool> saveUserProfile(IrxUserProfileEntity userProfile) async {
    _ensureInitialized();
    
    try {
      return await _prefs!.setString(_keyUserProfileData, json.encode(userProfile.toJson()));
    } catch (e) {
      print('保存用户资料失败: $e');
      return false;
    }
  }

  // 获取用户资料
  Future<IrxUserProfileEntity> getUserProfile() async {
    _ensureInitialized();
    
    try {
      final userString = _prefs!.getString(_keyUserProfileData);
      
      if (userString == null || userString.isEmpty) {
        // 返回默认用户资料
        return IrxUserProfileEntity.createDefault(id: 'default_user');
      }
      
      final userJson = json.decode(userString) as Map<String, dynamic>;
      return IrxUserProfileEntity.fromJson(userJson);
    } catch (e) {
      print('获取用户资料失败: $e');
      // 返回默认用户资料
      return IrxUserProfileEntity.createDefault(id: 'default_user');
    }
  }

  // ==================== 应用设置管理 ====================

  // 保存应用设置
  Future<bool> saveAppSettings(Map<String, dynamic> settings) async {
    _ensureInitialized();
    
    try {
      return await _prefs!.setString(_keyAppSettings, json.encode(settings));
    } catch (e) {
      print('保存应用设置失败: $e');
      return false;
    }
  }

  // 获取应用设置
  Future<Map<String, dynamic>> getAppSettings() async {
    _ensureInitialized();
    
    try {
      final settingsString = _prefs!.getString(_keyAppSettings);
      
      if (settingsString == null || settingsString.isEmpty) {
        return {
          'theme': 'light',
          'language': 'zh',
          'notifications': true,
          'autoSave': true,
        };
      }
      
      return json.decode(settingsString) as Map<String, dynamic>;
    } catch (e) {
      print('获取应用设置失败: $e');
      return {};
    }
  }

  // ==================== 清理方法 ====================

  // 清除所有数据
  Future<bool> clearAllData() async {
    _ensureInitialized();
    
    try {
      return await _prefs!.clear();
    } catch (e) {
      print('清除所有数据失败: $e');
      return false;
    }
  }
}
