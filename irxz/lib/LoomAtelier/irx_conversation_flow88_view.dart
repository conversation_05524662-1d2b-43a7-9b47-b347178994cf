// 对话交流页面 - 奢华iOS设计系统
// 遵循Luxury iOS Design System和命名差异化规则

import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'dart:math' as math;
import '../FabricVault/irx_ai_expert_entity.dart';
import '../FabricVault/irx_chat_record_entity.dart';
import '../FabricVault/irx_user_profile_entity.dart';
import '../IronLogic/irx_moonshot_api_service.dart';
import '../IronLogic/irx_local_storage_service.dart';

class IrxConversationFlow88View extends StatefulWidget {
  final IrxAiExpertEntity expert;

  const IrxConversationFlow88View({
    Key? key,
    required this.expert,
  }) : super(key: key);

  @override
  State<IrxConversationFlow88View> createState() => _IrxConversationFlow88ViewState();
}

class _IrxConversationFlow88ViewState extends State<IrxConversationFlow88View>
    with TickerProviderStateMixin {
  
  // 服务实例
  final IrxMoonshotApiService _apiService = IrxMoonshotApiService();
  final IrxLocalStorageService _storageService = IrxLocalStorageService();
  
  // 控制器
  final TextEditingController _messageContr77 = TextEditingController();
  final ScrollController _scrollContr66 = ScrollController();
  
  // 动画控制器
  late AnimationController _fadeContr55;
  late AnimationController _sendBtnContr44;
  late AnimationController _typingContr33;
  
  // 动画对象
  late Animation<double> _fadeAnim99;
  late Animation<double> _sendBtnScale88;
  late Animation<double> _typingOpacity77;
  
  // 状态变量
  List<IrxChatRecordEntity> _conversationList66 = [];
  IrxUserProfileEntity? _currentUser55;
  IrxAiExpertEntity? _currentExpert77; // 当前专家状态
  bool _isAiTyping44 = false;
  bool _isSending33 = false;
  String _currentInput22 = '';
  
  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _initializeData();
  }

  void _initializeAnimations() {
    // 淡入动画
    _fadeContr55 = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    
    // 发送按钮动画
    _sendBtnContr44 = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    
    // 打字动画
    _typingContr33 = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    // 创建动画对象
    _fadeAnim99 = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeContr55, curve: Curves.easeOut),
    );
    
    _sendBtnScale88 = Tween<double>(begin: 1.0, end: 0.9).animate(
      CurvedAnimation(parent: _sendBtnContr44, curve: Curves.easeInOut),
    );
    
    _typingOpacity77 = Tween<double>(begin: 0.3, end: 1.0).animate(
      CurvedAnimation(parent: _typingContr33, curve: Curves.easeInOut),
    );

    // 启动动画
    _fadeContr55.forward();
  }

  Future<void> _initializeData() async {
    try {
      // 初始化存储服务
      await _storageService.initialize();
      
      // 加载用户资料
      _currentUser55 = await _storageService.getUserProfile();

      // 加载最新的专家数据（包含使用次数）
      final experts = await _storageService.getExpertUsageData();
      _currentExpert77 = experts.firstWhere(
        (e) => e.id == widget.expert.id,
        orElse: () => widget.expert,
      );

      // 加载历史对话记录
      final records = await _storageService.getConversationRecords(widget.expert.id);

      setState(() {
        _conversationList66 = records;
      });
      
      // 滚动到底部
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _scrollToBottom();
      });
      
    } catch (e) {
      print('初始化数据失败: $e');
      _showErrorSnackBar('加载数据失败，请重试');
    }
  }

  Color _getCategoryColor() {
    switch (widget.expert.category) {
      case 'Fabric Care':
        return const Color(0xFF3498DB); // 蒸汽蓝
      case 'Quick Tips':
        return const Color(0xFF1ABC9C); // 青绿
      case 'Garment Care':
        return const Color(0xFFE74C3C); // 热红
      case 'Tools':
        return const Color(0xFF9B59B6); // 紫色
      case 'Prevention':
        return const Color(0xFFF39C12); // 橙色
      default:
        return const Color(0xFF3498DB);
    }
  }

  void _scrollToBottom() {
    if (_scrollContr66.hasClients) {
      _scrollContr66.animateTo(
        _scrollContr66.position.maxScrollExtent,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: const Color(0xFFE74C3C),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: const Color(0xFF27AE60),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _fadeContr55.dispose();
    _sendBtnContr44.dispose();
    _typingContr33.dispose();
    _messageContr77.dispose();
    _scrollContr66.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      appBar: _buildAppBar(),
      body: AnimatedBuilder(
        animation: _fadeAnim99,
        builder: (context, child) {
          return Opacity(
            opacity: _fadeAnim99.value,
            child: Column(
              children: [
                Expanded(
                  child: _buildConversationList(),
                ),
                _buildInputArea(),
              ],
            ),
          );
        },
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: Colors.white,
      elevation: 0,
      leading: GestureDetector(
        onTap: () {
          HapticFeedback.lightImpact();
          Navigator.of(context).pop();
        },
        child: Container(
          margin: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: const Color(0xFFF8F9FA),
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: const Icon(
            CupertinoIcons.back,
            color: Color(0xFF2C3E50),
            size: 20,
          ),
        ),
      ),
      title: Row(
        children: [
          // AI专家头像
          Container(
            width: 36,
            height: 36,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                color: _getCategoryColor(),
                width: 2,
              ),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(17),
              child: Image.asset(
                widget.expert.avatarPath,
                width: 32,
                height: 32,
                fit: BoxFit.cover,
              ),
            ),
          ),
          
          const SizedBox(width: 12),
          
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.expert.displayName,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF2C3E50),
                  ),
                ),
                Text(
                  widget.expert.category,
                  style: TextStyle(
                    fontSize: 12,
                    color: _getCategoryColor(),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      actions: [
        // 免费次数显示
        Container(
          margin: const EdgeInsets.only(right: 16),
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: (_currentExpert77?.hasFreeTries ?? widget.expert.hasFreeTries)
                ? _getCategoryColor().withOpacity(0.1)
                : const Color(0xFFE74C3C).withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            '${_currentExpert77?.remainingFreeTries ?? widget.expert.remainingFreeTries} left',
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: (_currentExpert77?.hasFreeTries ?? widget.expert.hasFreeTries)
                  ? _getCategoryColor()
                  : const Color(0xFFE74C3C),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildConversationList() {
    if (_conversationList66.isEmpty) {
      return _buildEmptyState();
    }

    return ListView.builder(
      controller: _scrollContr66,
      padding: const EdgeInsets.all(16),
      itemCount: _conversationList66.length + (_isAiTyping44 ? 1 : 0),
      itemBuilder: (context, index) {
        if (index == _conversationList66.length && _isAiTyping44) {
          return _buildTypingIndicator();
        }

        final record = _conversationList66[index];
        return _buildMessageBubble(record, index);
      },
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              color: _getCategoryColor().withOpacity(0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              CupertinoIcons.chat_bubble_2_fill,
              size: 40,
              color: _getCategoryColor(),
            ),
          ),

          const SizedBox(height: 24),

          Text(
            'Start Your Conversation',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF2C3E50),
            ),
          ),

          const SizedBox(height: 8),

          Text(
            'Ask ${widget.expert.displayName} anything about ironing!',
            style: const TextStyle(
              fontSize: 14,
              color: Color(0xFF7F8C8D),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildMessageBubble(IrxChatRecordEntity record, int index) {
    final isUser = record.isUserMessage;
    final isLastMessage = index == _conversationList66.length - 1;

    return Container(
      margin: EdgeInsets.only(
        bottom: isLastMessage ? 16 : 8,
        left: isUser ? 60 : 0,
        right: isUser ? 0 : 60,
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: isUser ? MainAxisAlignment.end : MainAxisAlignment.start,
        children: [
          if (!isUser) ...[
            // AI头像
            Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: _getCategoryColor(),
                  width: 1.5,
                ),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(15),
                child: Image.asset(
                  widget.expert.avatarPath,
                  width: 29,
                  height: 29,
                  fit: BoxFit.cover,
                ),
              ),
            ),
            const SizedBox(width: 8),
          ],

          Flexible(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                color: isUser
                    ? _getCategoryColor()
                    : Colors.white,
                borderRadius: BorderRadius.only(
                  topLeft: const Radius.circular(16),
                  topRight: const Radius.circular(16),
                  bottomLeft: Radius.circular(isUser ? 16 : 4),
                  bottomRight: Radius.circular(isUser ? 4 : 16),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.08),
                    blurRadius: 12,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    record.content,
                    style: TextStyle(
                      fontSize: 14,
                      color: isUser ? Colors.white : const Color(0xFF2C3E50),
                      height: 1.4,
                    ),
                  ),

                  const SizedBox(height: 4),

                  Text(
                    record.formattedTime,
                    style: TextStyle(
                      fontSize: 11,
                      color: isUser
                          ? Colors.white.withOpacity(0.7)
                          : const Color(0xFF95A5A6),
                    ),
                  ),
                ],
              ),
            ),
          ),

          if (isUser) ...[
            const SizedBox(width: 8),
            // 用户头像
            Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: _getCategoryColor(),
                  width: 1.5,
                ),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(15),
                child: Image.asset(
                  _currentUser55?.avatarPath ?? 'assets/Irx/avatars/avatar_1.jpg',
                  width: 29,
                  height: 29,
                  fit: BoxFit.cover,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildTypingIndicator() {
    return Container(
      margin: const EdgeInsets.only(bottom: 16, right: 60),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // AI头像
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                color: _getCategoryColor(),
                width: 1.5,
              ),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(15),
              child: Image.asset(
                widget.expert.avatarPath,
                width: 29,
                height: 29,
                fit: BoxFit.cover,
              ),
            ),
          ),

          const SizedBox(width: 8),

          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
                bottomLeft: Radius.circular(4),
                bottomRight: Radius.circular(16),
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.08),
                  blurRadius: 12,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: AnimatedBuilder(
              animation: _typingOpacity77,
              builder: (context, child) {
                return Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    _buildTypingDot(0),
                    const SizedBox(width: 4),
                    _buildTypingDot(1),
                    const SizedBox(width: 4),
                    _buildTypingDot(2),
                  ],
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTypingDot(int index) {
    return AnimatedBuilder(
      animation: _typingContr33,
      builder: (context, child) {
        final animationValue = (_typingContr33.value + index * 0.2) % 1.0;
        final opacity = (math.sin(animationValue * math.pi * 2) + 1) / 2;

        return Container(
          width: 6,
          height: 6,
          decoration: BoxDecoration(
            color: _getCategoryColor().withOpacity(0.3 + opacity * 0.7),
            shape: BoxShape.circle,
          ),
        );
      },
    );
  }

  Widget _buildInputArea() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 20,
            offset: const Offset(0, -4),
          ),
        ],
      ),
      child: SafeArea(
        child: Row(
          children: [
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  color: const Color(0xFFF8F9FA),
                  borderRadius: BorderRadius.circular(24),
                  border: Border.all(
                    color: _currentInput22.isNotEmpty
                        ? _getCategoryColor().withOpacity(0.3)
                        : Colors.transparent,
                    width: 1,
                  ),
                ),
                child: TextField(
                  controller: _messageContr77,
                  maxLines: null,
                  textInputAction: TextInputAction.send,
                  onChanged: (value) {
                    setState(() {
                      _currentInput22 = value;
                    });
                  },
                  onSubmitted: (value) {
                    if (value.trim().isNotEmpty) {
                      _sendMessage();
                    }
                  },
                  decoration: InputDecoration(
                    hintText: 'Ask about ironing techniques...',
                    hintStyle: const TextStyle(
                      color: Color(0xFF95A5A6),
                      fontSize: 14,
                    ),
                    border: InputBorder.none,
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 20,
                      vertical: 12,
                    ),
                  ),
                  style: const TextStyle(
                    fontSize: 14,
                    color: Color(0xFF2C3E50),
                  ),
                ),
              ),
            ),

            const SizedBox(width: 12),

            // 发送按钮
            AnimatedBuilder(
              animation: _sendBtnScale88,
              builder: (context, child) {
                return Transform.scale(
                  scale: _sendBtnScale88.value,
                  child: GestureDetector(
                    onTapDown: (_) => _sendBtnContr44.forward(),
                    onTapUp: (_) => _sendBtnContr44.reverse(),
                    onTapCancel: () => _sendBtnContr44.reverse(),
                    onTap: _currentInput22.trim().isNotEmpty && !_isSending33
                        ? _sendMessage
                        : null,
                    child: Container(
                      width: 48,
                      height: 48,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: _currentInput22.trim().isNotEmpty && !_isSending33
                              ? [
                                  _getCategoryColor(),
                                  _getCategoryColor().withOpacity(0.8),
                                ]
                              : [
                                  const Color(0xFFBDC3C7),
                                  const Color(0xFF95A5A6),
                                ],
                        ),
                        shape: BoxShape.circle,
                        boxShadow: _currentInput22.trim().isNotEmpty && !_isSending33
                            ? [
                                BoxShadow(
                                  color: _getCategoryColor().withOpacity(0.3),
                                  blurRadius: 12,
                                  offset: const Offset(0, 4),
                                ),
                              ]
                            : [],
                      ),
                      child: _isSending33
                          ? const Center(
                              child: SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                  strokeWidth: 2,
                                ),
                              ),
                            )
                          : const Icon(
                              CupertinoIcons.paperplane_fill,
                              color: Colors.white,
                              size: 20,
                            ),
                    ),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  // 发送消息
  Future<void> _sendMessage() async {
    final messageText = _messageContr77.text.trim();
    if (messageText.isEmpty || _isSending33) return;

    // 检查免费次数 - 如果没有次数，只显示提示但不阻止发送
    final hasFreeTries = _currentExpert77?.hasFreeTries ?? widget.expert.hasFreeTries;
    if (!hasFreeTries) {
      _showErrorSnackBar('Free attempts exhausted. Please upgrade to premium version.');
      // 不返回，继续执行发送逻辑，但不会扣除次数
    }

    setState(() {
      _isSending33 = true;
    });

    try {
      // 只有在有免费次数时才清空输入框
      if (hasFreeTries) {
        _messageContr77.clear();
        setState(() {
          _currentInput22 = '';
        });
      }

      // 创建用户消息记录
      final userMessage = IrxChatRecordEntity.userMessage(
        id: 'user_${DateTime.now().millisecondsSinceEpoch}',
        expertId: widget.expert.id,
        expertCodeName: widget.expert.codeName,
        content: messageText,
      );

      // 添加到对话列表并保存
      setState(() {
        _conversationList66.add(userMessage);
      });

      await _storageService.saveConversationRecord(userMessage);
      _scrollToBottom();

      // 开始AI打字动画
      setState(() {
        _isAiTyping44 = true;
      });
      _typingContr33.repeat();
      _scrollToBottom();

      // 调用API获取AI回复
      final aiReply = await _apiService.sendConversation(
        expert: widget.expert,
        conversationHistory: _conversationList66,
        userMessage: messageText,
      );

      // 停止打字动画
      _typingContr33.stop();
      setState(() {
        _isAiTyping44 = false;
      });

      // 创建AI回复记录
      final aiMessage = IrxChatRecordEntity.aiMessage(
        id: 'ai_${DateTime.now().millisecondsSinceEpoch}',
        expertId: widget.expert.id,
        expertCodeName: widget.expert.codeName,
        content: aiReply,
      );

      // 添加到对话列表并保存
      setState(() {
        _conversationList66.add(aiMessage);
      });

      await _storageService.saveConversationRecord(aiMessage);

      // 扣除免费次数（只有在有免费次数且AI成功回复后才扣除）
      if (hasFreeTries) {
        await _storageService.updateExpertUsageCount(widget.expert.id);

        // 重新加载专家数据以更新UI
        final experts = await _storageService.getExpertUsageData();
        _currentExpert77 = experts.firstWhere(
          (e) => e.id == widget.expert.id,
          orElse: () => widget.expert,
        );
      }

      _scrollToBottom();
      _showSuccessSnackBar('Reply received successfully');

    } catch (e) {
      // 停止打字动画
      _typingContr33.stop();
      setState(() {
        _isAiTyping44 = false;
      });

      String errorMessage = 'Send failed, please try again';
      if (e is IrxApiException) {
        errorMessage = e.userFriendlyMessage;
      }

      _showErrorSnackBar(errorMessage);

      // 如果发送失败，恢复输入框内容
      if (hasFreeTries) {
        _messageContr77.text = messageText;
        setState(() {
          _currentInput22 = messageText;
        });
      }

    } finally {
      setState(() {
        _isSending33 = false;
      });
    }
  }
}
