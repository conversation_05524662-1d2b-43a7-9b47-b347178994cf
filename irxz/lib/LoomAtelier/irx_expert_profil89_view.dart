// 专家角色档案页面 - 奢华iOS设计系统
// 遵循Luxury iOS Design System和命名差异化规则

import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import '../FabricVault/irx_ai_expert_entity.dart';

class IrxExpertProfil89View extends StatefulWidget {
  final IrxAiExpertEntity expert;
  final VoidCallback? onStartChat;

  const IrxExpertProfil89View({
    Key? key,
    required this.expert,
    this.onStartChat,
  }) : super(key: key);

  @override
  State<IrxExpertProfil89View> createState() => _IrxExpertProfil89ViewState();
}

class _IrxExpertProfil89ViewState extends State<IrxExpertProfil89View>
    with TickerProviderStateMixin {
  
  // 动画控制器
  late AnimationController _fadeContr88;
  late AnimationController _scaleContr77;
  late AnimationController _shimmerContr66;
  
  // 动画对象
  late Animation<double> _fadeAnim99;
  late Animation<double> _scaleAnim88;
  late Animation<double> _shimmerAnim77;
  
  // 状态变量
  bool _isLoading55 = false;
  bool _showFullDesc44 = true; // 默认显示详细介绍

  @override
  void initState() {
    super.initState();
    _initAnimations();
    _startEntryAnimation();
  }

  void _initAnimations() {
    // 淡入动画控制器
    _fadeContr88 = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    // 缩放动画控制器
    _scaleContr77 = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    
    // 闪烁动画控制器
    _shimmerContr66 = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    // 创建动画对象
    _fadeAnim99 = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeContr88,
      curve: Curves.easeOutCubic,
    ));

    _scaleAnim88 = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _scaleContr77,
      curve: Curves.elasticOut,
    ));

    _shimmerAnim77 = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _shimmerContr66,
      curve: Curves.easeInOut,
    ));
  }

  void _startEntryAnimation() {
    Future.delayed(const Duration(milliseconds: 100), () {
      _fadeContr88.forward();
    });
    
    Future.delayed(const Duration(milliseconds: 200), () {
      _scaleContr77.forward();
    });
  }

  void _triggerShimmerEffect() {
    HapticFeedback.lightImpact();
    _shimmerContr66.reset();
    _shimmerContr66.forward();
  }

  Color _getCategoryColor() {
    switch (widget.expert.category) {
      case 'Fabric Care':
        return const Color(0xFF3498DB); // 蒸汽蓝
      case 'Quick Tips':
        return const Color(0xFF1ABC9C); // 青绿
      case 'Garment Care':
        return const Color(0xFFE74C3C); // 热红
      case 'Tools':
        return const Color(0xFF9B59B6); // 紫色
      case 'Prevention':
        return const Color(0xFFF39C12); // 橙色
      default:
        return const Color(0xFF3498DB);
    }
  }

  @override
  void dispose() {
    _fadeContr88.dispose();
    _scaleContr77.dispose();
    _shimmerContr66.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      body: AnimatedBuilder(
        animation: _fadeAnim99,
        builder: (context, child) {
          return Opacity(
            opacity: _fadeAnim99.value,
            child: _buildMainContent(),
          );
        },
      ),
    );
  }

  Widget _buildMainContent() {
    return CustomScrollView(
      physics: const BouncingScrollPhysics(),
      slivers: [
        _buildSliverAppBar(),
        SliverToBoxAdapter(
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildExpertCard(),
                const SizedBox(height: 24),
                _buildActionButton(),
                const SizedBox(height: 24),
                _buildDescriptionCard(),
                const SizedBox(height: 24),
                _buildSpecialtyCard(),
                const SizedBox(height: 40),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSliverAppBar() {
    return SliverAppBar(
      expandedHeight: 60,
      floating: false,
      pinned: true,
      backgroundColor: Colors.white.withOpacity(0.95),
      elevation: 0,
      leading: GestureDetector(
        onTap: () {
          HapticFeedback.lightImpact();
          Navigator.of(context).pop();
        },
        child: Container(
          margin: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.white,
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: const Icon(
            CupertinoIcons.back,
            color: Color(0xFF2C3E50),
            size: 20,
          ),
        ),
      ),
      title: Text(
        'Expert Profile',
        style: TextStyle(
          color: const Color(0xFF2C3E50),
          fontSize: 18,
          fontWeight: FontWeight.w600,
        ),
      ),
      centerTitle: true,
    );
  }

  Widget _buildExpertCard() {
    return AnimatedBuilder(
      animation: _scaleAnim88,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnim88.value,
          child: Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.08),
                  blurRadius: 20,
                  offset: const Offset(0, 8),
                ),
                BoxShadow(
                  color: _getCategoryColor().withOpacity(0.1),
                  blurRadius: 30,
                  offset: const Offset(0, 12),
                ),
              ],
            ),
            child: Row(
              children: [
                // 头像容器
                Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: _getCategoryColor(),
                      width: 3,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: _getCategoryColor().withOpacity(0.3),
                        blurRadius: 16,
                        offset: const Offset(0, 6),
                      ),
                    ],
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(37),
                    child: Image.asset(
                      widget.expert.avatarPath,
                      width: 74,
                      height: 74,
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
                
                const SizedBox(width: 20),
                
                // 专家信息
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.expert.displayName,
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.w700,
                          color: Color(0xFF2C3E50),
                          height: 1.2,
                        ),
                      ),
                      
                      const SizedBox(height: 8),
                      
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 6,
                        ),
                        decoration: BoxDecoration(
                          color: _getCategoryColor().withOpacity(0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          widget.expert.category,
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                            color: _getCategoryColor(),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildDescriptionCard() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.06),
            blurRadius: 16,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                CupertinoIcons.doc_text_fill,
                color: _getCategoryColor(),
                size: 20,
              ),
              const SizedBox(width: 8),
              const Text(
                'Expert Description',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF2C3E50),
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          Text(
            widget.expert.longDesc,
            style: const TextStyle(
              fontSize: 14,
              color: Color(0xFF7F8C8D),
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSpecialtyCard() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            _getCategoryColor().withOpacity(0.05),
            _getCategoryColor().withOpacity(0.1),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: _getCategoryColor().withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                CupertinoIcons.sparkles,
                color: _getCategoryColor(),
                size: 20,
              ),
              const SizedBox(width: 8),
              const Text(
                'Specialty Focus',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF2C3E50),
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          _buildSpecialtyItem(
            CupertinoIcons.checkmark_seal_fill,
            'Professional Guidance',
            'Expert-level ironing techniques',
          ),

          const SizedBox(height: 12),

          _buildSpecialtyItem(
            CupertinoIcons.clock_fill,
            'Time Efficient',
            'Quick and effective methods',
          ),

          const SizedBox(height: 12),

          _buildSpecialtyItem(
            CupertinoIcons.shield_fill,
            'Fabric Safe',
            'Protect your garments',
          ),
        ],
      ),
    );
  }

  Widget _buildSpecialtyItem(IconData icon, String title, String subtitle) {
    return Row(
      children: [
        Container(
          width: 32,
          height: 32,
          decoration: BoxDecoration(
            color: _getCategoryColor().withOpacity(0.15),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color: _getCategoryColor(),
            size: 16,
          ),
        ),

        const SizedBox(width: 12),

        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF2C3E50),
                ),
              ),
              Text(
                subtitle,
                style: const TextStyle(
                  fontSize: 12,
                  color: Color(0xFF7F8C8D),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildActionButton() {
    return AnimatedBuilder(
      animation: _shimmerAnim77,
      builder: (context, child) {
        return GestureDetector(
          onTap: () {
            _triggerShimmerEffect();
            setState(() {
              _isLoading55 = true;
            });

            // 模拟加载延迟
            Future.delayed(const Duration(milliseconds: 800), () {
              if (mounted) {
                setState(() {
                  _isLoading55 = false;
                });

                // 执行聊天回调
                if (widget.onStartChat != null) {
                  widget.onStartChat!();
                }

                // 返回上一页
                Navigator.of(context).pop();
              }
            });
          },
          child: Container(
            width: double.infinity,
            height: 56,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  _getCategoryColor(),
                  _getCategoryColor().withOpacity(0.8),
                ],
              ),
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: _getCategoryColor().withOpacity(0.3),
                  blurRadius: 16,
                  offset: const Offset(0, 8),
                ),
                // 闪烁效果
                if (_shimmerAnim77.value > 0)
                  BoxShadow(
                    color: _getCategoryColor().withOpacity(
                      0.6 * _shimmerAnim77.value,
                    ),
                    blurRadius: 24,
                    offset: const Offset(0, 0),
                  ),
              ],
            ),
            child: _isLoading55
                ? const Center(
                    child: SizedBox(
                      width: 24,
                      height: 24,
                      child: CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        strokeWidth: 2,
                      ),
                    ),
                  )
                : Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        CupertinoIcons.chat_bubble_2_fill,
                        color: Colors.white,
                        size: 20,
                      ),
                      const SizedBox(width: 12),
                      const Text(
                        'Start Conversation',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.white,
                        ),
                      ),
                    ],
                  ),
          ),
        );
      },
    );
  }
}
