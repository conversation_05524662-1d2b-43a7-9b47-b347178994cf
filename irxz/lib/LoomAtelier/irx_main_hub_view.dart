// 主页角色选择界面 - 16:9竖屏大图卡片信息流
// 遵循熨烫主题命名和差异化规则

import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';

import '../SteamCore/irx_app_state.dart';
import '../SteamCore/irx_press_actions.dart';
import '../FabricVault/irx_ai_expert_entity.dart';
import '../WeaveParts/irx_expert_card_widget.dart';
import '../WeaveParts/irx_category_filter_widget.dart';
import '../WeaveParts/irx_steam_app_bar.dart';

class IrxMainHubView extends StatefulWidget {
  const IrxMainHubView({super.key});

  @override
  State<IrxMainHubView> createState() => _IrxMainHubViewState();
}

class _IrxMainHubViewState extends State<IrxMainHubView>
    with TickerProviderStateMixin {
  
  // 动画控制器
  late AnimationController _fadeController;
  late AnimationController _slideController;
  
  // 筛选状态
  String _selectedCategory = 'All';
  List<IrxAiExpertEntity> _filteredExperts = [];
  
  // 搜索控制器
  final TextEditingController _searchController = TextEditingController();
  bool _isSearching = false;

  @override
  void initState() {
    super.initState();
    
    // 初始化动画控制器
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    
    // 启动进入动画
    _fadeController.forward();
    _slideController.forward();
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _slideController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  // 筛选专家列表
  void _filterExperts(List<IrxAiExpertEntity> allExperts) {
    setState(() {
      if (_selectedCategory == 'All') {
        _filteredExperts = allExperts;
      } else {
        _filteredExperts = allExperts
            .where((expert) => expert.category == _selectedCategory)
            .toList();
      }
      
      // 如果有搜索关键词，进一步筛选
      if (_searchController.text.isNotEmpty) {
        final query = _searchController.text.toLowerCase();
        _filteredExperts = _filteredExperts.where((expert) {
          return expert.displayName.toLowerCase().contains(query) ||
                 expert.shortDesc.toLowerCase().contains(query) ||
                 expert.codeName.toLowerCase().contains(query);
        }).toList();
      }
    });
  }

  // 处理专家选择
  void _onExpertSelected(IrxAiExpertEntity expert) {
    // 触发选择动画
    HapticFeedback.lightImpact();
    
    // 更新Redux状态
    StoreProvider.of<IrxAppState>(context).dispatch(
      SelectAiExpertAction(expert),
    );
    
    // 导航到聊天页面
    Navigator.pushNamed(
      context, 
      '/chat',
      arguments: expert,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5), // 浅灰背景
      body: StoreConnector<IrxAppState, List<IrxAiExpertEntity>>(
        converter: (store) => store.state.aiExperts,
        onInit: (store) {
          // 初始化时设置筛选列表
          _filteredExperts = store.state.aiExperts;
        },
        onWillChange: (oldExperts, newExperts) {
          // 当专家列表变化时重新筛选
          if (newExperts != null) {
            _filterExperts(newExperts);
          }
        },
        builder: (context, experts) {
          return CustomScrollView(
            physics: const BouncingScrollPhysics(),
            slivers: [
              // 自定义AppBar
              IrxSteamAppBar(
                title: 'Ironing Experts',
                isSearching: _isSearching,
                searchController: _searchController,
                onSearchToggle: () {
                  setState(() {
                    _isSearching = !_isSearching;
                    if (!_isSearching) {
                      _searchController.clear();
                      _filterExperts(experts);
                    }
                  });
                },
                onSearchChanged: (query) {
                  _filterExperts(experts);
                },
              ),
              
              // 分类筛选器
              SliverToBoxAdapter(
                child: IrxCategoryFilterWidget(
                  selectedCategory: _selectedCategory,
                  onCategoryChanged: (category) {
                    setState(() {
                      _selectedCategory = category;
                    });
                    _filterExperts(experts);
                  },
                ).animate()
                  .fadeIn(duration: 600.ms, delay: 200.ms)
                  .slideY(begin: -0.3, end: 0),
              ),
              
              // 专家卡片网格
              SliverPadding(
                padding: const EdgeInsets.all(20),
                sliver: SliverGrid(
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 1, // 单列显示，突出16:9大图效果
                    mainAxisSpacing: 24,
                    childAspectRatio: 9 / 16, // 16:9竖屏比例
                  ),
                  delegate: SliverChildBuilderDelegate(
                    (context, index) {
                      if (index >= _filteredExperts.length) return null;
                      
                      final expert = _filteredExperts[index];
                      
                      return AnimationConfiguration.staggeredGrid(
                        position: index,
                        duration: const Duration(milliseconds: 800),
                        columnCount: 1,
                        child: SlideAnimation(
                          verticalOffset: 50.0,
                          child: FadeInAnimation(
                            child: IrxExpertCardWidget(
                              expert: expert,
                              onTap: () => _onExpertSelected(expert),
                              cardIndex: index,
                            ),
                          ),
                        ),
                      );
                    },
                    childCount: _filteredExperts.length,
                  ),
                ),
              ),
              
              // 底部间距
              const SliverToBoxAdapter(
                child: SizedBox(height: 100),
              ),
            ],
          );
        },
      ),
      
      // 浮动操作按钮 - 快速访问待办清单
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () {
          Navigator.pushNamed(context, '/tasks');
        },
        backgroundColor: const Color(0xFF3498DB), // 蒸汽蓝
        foregroundColor: Colors.white,
        icon: const Icon(CupertinoIcons.list_bullet),
        label: const Text(
          'Quick Tasks',
          style: TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 14,
          ),
        ),
      ).animate()
        .scale(delay: 1000.ms, duration: 400.ms)
        .shimmer(delay: 1200.ms, duration: 1000.ms),
    );
  }
}

// 触觉反馈工具类
class HapticFeedback {
  static void lightImpact() {
    // iOS风格触觉反馈
    // 在实际项目中可以使用 haptic_feedback 包
  }
  
  static void mediumImpact() {
    // 中等强度触觉反馈
  }
  
  static void heavyImpact() {
    // 强烈触觉反馈
  }
}
