// 主页角色选择界面 - 16:9竖屏大图卡片信息流
// 遵循熨烫主题命名和差异化规则

import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';

import '../SteamCore/irx_app_state.dart';
import '../SteamCore/irx_press_actions.dart';
import '../FabricVault/irx_ai_expert_entity.dart';
import '../WeaveParts/irx_expert_card_widget.dart';
import '../WeaveParts/irx_category_filter_widget.dart';
import '../WeaveParts/irx_steam_app_bar.dart';

class IrxMainHubView extends StatefulWidget {
  const IrxMainHubView({super.key});

  @override
  State<IrxMainHubView> createState() => _IrxMainHubViewState();
}

class _IrxMainHubViewState extends State<IrxMainHubView>
    with TickerProviderStateMixin {

  // 动画控制器
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late AnimationController _cardSwitchController;

  // 筛选状态
  String _selectedCategory = 'All';
  List<IrxAiExpertEntity> _filteredExperts = [];
  int _selectedExpertIndex = 0; // 当前选中的专家索引

  // 搜索控制器
  final TextEditingController _searchController = TextEditingController();
  bool _isSearching = false;

  @override
  void initState() {
    super.initState();

    // 初始化动画控制器
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _slideController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _cardSwitchController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    // 启动进入动画
    _fadeController.forward();
    _slideController.forward();
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _slideController.dispose();
    _cardSwitchController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  // 筛选专家列表
  void _filterExperts(List<IrxAiExpertEntity> allExperts) {
    setState(() {
      if (_selectedCategory == 'All') {
        _filteredExperts = allExperts;
      } else {
        _filteredExperts = allExperts
            .where((expert) => expert.category == _selectedCategory)
            .toList();
      }

      // 如果有搜索关键词，进一步筛选
      if (_searchController.text.isNotEmpty) {
        final query = _searchController.text.toLowerCase();
        _filteredExperts = _filteredExperts.where((expert) {
          return expert.displayName.toLowerCase().contains(query) ||
                 expert.shortDesc.toLowerCase().contains(query) ||
                 expert.codeName.toLowerCase().contains(query);
        }).toList();
      }

      // 重置选中索引
      if (_selectedExpertIndex >= _filteredExperts.length) {
        _selectedExpertIndex = 0;
      }
    });
  }

  // 选择专家
  void _selectExpert(int index) {
    if (index != _selectedExpertIndex && index < _filteredExperts.length) {
      setState(() {
        _selectedExpertIndex = index;
      });

      // 触发卡片切换动画
      _cardSwitchController.forward().then((_) {
        _cardSwitchController.reverse();
      });

      // 触觉反馈
      HapticFeedback.selectionClick();
    }
  }

  // 处理专家选择
  void _onExpertSelected(IrxAiExpertEntity expert) {
    // 触发选择动画
    HapticFeedback.lightImpact();

    // 更新Redux状态
    StoreProvider.of<IrxAppState>(context).dispatch(
      SelectAiExpertAction(expert),
    );

    // 导航到聊天页面
    Navigator.pushNamed(
      context,
      '/chat',
      arguments: expert,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5), // 浅灰背景
      body: StoreConnector<IrxAppState, List<IrxAiExpertEntity>>(
        converter: (store) => store.state.aiExperts,
        onInit: (store) {
          // 初始化时设置筛选列表
          _filteredExperts = store.state.aiExperts;
        },
        onWillChange: (oldExperts, newExperts) {
          // 当专家列表变化时重新筛选
          if (newExperts != null) {
            _filterExperts(newExperts);
          }
        },
        builder: (context, experts) {
          if (_filteredExperts.isEmpty) {
            return const Center(
              child: CircularProgressIndicator(
                color: Color(0xFF3498DB),
              ),
            );
          }

          final selectedExpert = _filteredExperts.isNotEmpty
              ? _filteredExperts[_selectedExpertIndex]
              : null;

          return CustomScrollView(
            physics: const BouncingScrollPhysics(),
            slivers: [
              // 自定义AppBar
              IrxSteamAppBar(
                title: 'Ironing Experts',
                isSearching: _isSearching,
                searchController: _searchController,
                onSearchToggle: () {
                  setState(() {
                    _isSearching = !_isSearching;
                    if (!_isSearching) {
                      _searchController.clear();
                      _filterExperts(experts);
                    }
                  });
                },
                onSearchChanged: (query) {
                  _filterExperts(experts);
                },
              ),

              // 分类筛选器
              SliverToBoxAdapter(
                child: IrxCategoryFilterWidget(
                  selectedCategory: _selectedCategory,
                  onCategoryChanged: (category) {
                    setState(() {
                      _selectedCategory = category;
                    });
                    _filterExperts(experts);
                  },
                ).animate()
                  .fadeIn(duration: 600.ms, delay: 200.ms)
                  .slideY(begin: -0.3, end: 0),
              ),

              // 专家头像选择器
              SliverToBoxAdapter(
                child: _buildExpertAvatarSelector(),
              ),

              // 选中专家的大卡片
              if (selectedExpert != null)
                SliverToBoxAdapter(
                  child: _buildSelectedExpertCard(selectedExpert),
                ),

              // 底部间距
              const SliverToBoxAdapter(
                child: SizedBox(height: 100),
              ),
            ],
          );
        },
      ),
      
      // 浮动操作按钮 - 快速访问待办清单
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () {
          Navigator.pushNamed(context, '/tasks');
        },
        backgroundColor: const Color(0xFF3498DB), // 蒸汽蓝
        foregroundColor: Colors.white,
        icon: const Icon(CupertinoIcons.list_bullet),
        label: const Text(
          'Quick Tasks',
          style: TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 14,
          ),
        ),
      ).animate()
        .scale(delay: 1000.ms, duration: 400.ms)
        .shimmer(delay: 1200.ms, duration: 1000.ms),
    );
  }

  // 构建专家头像选择器
  Widget _buildExpertAvatarSelector() {
    return Container(
      height: 120,
      margin: const EdgeInsets.symmetric(vertical: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Text(
              'Select Expert',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Color(0xFF2C3E50),
              ),
            ),
          ),

          const SizedBox(height: 12),

          // 头像列表
          Expanded(
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: _filteredExperts.length,
              itemBuilder: (context, index) {
                final expert = _filteredExperts[index];
                final isSelected = index == _selectedExpertIndex;

                return Padding(
                  padding: const EdgeInsets.only(right: 16),
                  child: GestureDetector(
                    onTap: () => _selectExpert(index),
                    child: AnimatedContainer(
                      duration: const Duration(milliseconds: 300),
                      curve: Curves.easeInOut,
                      child: Column(
                        children: [
                          // 头像容器
                          Container(
                            width: 64,
                            height: 64,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              border: Border.all(
                                color: isSelected
                                    ? _getCategoryColor(expert.category)
                                    : Colors.transparent,
                                width: isSelected ? 3 : 0,
                              ),
                              boxShadow: isSelected ? [
                                BoxShadow(
                                  color: _getCategoryColor(expert.category).withOpacity(0.3),
                                  blurRadius: 12,
                                  offset: const Offset(0, 4),
                                ),
                              ] : null,
                            ),
                            child: ClipOval(
                              child: Image.asset(
                                expert.avatarPath,
                                fit: BoxFit.cover,
                                errorBuilder: (context, error, stackTrace) {
                                  return Container(
                                    decoration: BoxDecoration(
                                      shape: BoxShape.circle,
                                      gradient: LinearGradient(
                                        colors: [
                                          _getCategoryColor(expert.category).withOpacity(0.7),
                                          _getCategoryColor(expert.category),
                                        ],
                                      ),
                                    ),
                                    child: Icon(
                                      CupertinoIcons.person_fill,
                                      size: 30,
                                      color: Colors.white,
                                    ),
                                  );
                                },
                              ),
                            ),
                          ),

                          const SizedBox(height: 8),

                          // 专家代号
                          Text(
                            expert.codeName,
                            style: TextStyle(
                              fontSize: 10,
                              fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                              color: isSelected
                                  ? _getCategoryColor(expert.category)
                                  : const Color(0xFF7F8C8D),
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    ),
                  ).animate()
                    .scale(
                      duration: 200.ms,
                      begin: const Offset(1.0, 1.0),
                      end: isSelected ? const Offset(1.1, 1.1) : const Offset(1.0, 1.0),
                    ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  // 构建选中专家的大卡片
  Widget _buildSelectedExpertCard(IrxAiExpertEntity expert) {
    return AnimatedBuilder(
      animation: _cardSwitchController,
      builder: (context, child) {
        return Transform.scale(
          scale: 1.0 - (_cardSwitchController.value * 0.05),
          child: Opacity(
            opacity: 1.0 - (_cardSwitchController.value * 0.3),
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
              height: 500, // 大卡片高度
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(24),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.15),
                    blurRadius: 20,
                    offset: const Offset(0, 8),
                  ),
                  BoxShadow(
                    color: _getCategoryColor(expert.category).withOpacity(0.1),
                    blurRadius: 30,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(24),
                child: Stack(
                  children: [
                    // 背景渐变
                    Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            Colors.white,
                            _getCategoryColor(expert.category).withOpacity(0.05),
                          ],
                        ),
                      ),
                    ),

                    // 背景装饰圆圈
                    Positioned(
                      top: -80,
                      right: -80,
                      child: Container(
                        width: 200,
                        height: 200,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: _getCategoryColor(expert.category).withOpacity(0.08),
                        ),
                      ),
                    ),

                    // 主要内容
                    Padding(
                      padding: const EdgeInsets.all(32),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // 顶部信息栏
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              // 分类标签
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 16,
                                  vertical: 8,
                                ),
                                decoration: BoxDecoration(
                                  color: _getCategoryColor(expert.category).withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(20),
                                  border: Border.all(
                                    color: _getCategoryColor(expert.category).withOpacity(0.3),
                                    width: 1,
                                  ),
                                ),
                                child: Text(
                                  expert.category,
                                  style: TextStyle(
                                    color: _getCategoryColor(expert.category),
                                    fontSize: 14,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ),

                              // 剩余次数
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 12,
                                  vertical: 6,
                                ),
                                decoration: BoxDecoration(
                                  color: _getUsageColor(expert).withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(16),
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Icon(
                                      CupertinoIcons.chat_bubble_fill,
                                      size: 14,
                                      color: _getUsageColor(expert),
                                    ),
                                    const SizedBox(width: 6),
                                    Text(
                                      '${expert.remainingFreeTries} left',
                                      style: TextStyle(
                                        color: _getUsageColor(expert),
                                        fontSize: 12,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),

                          const SizedBox(height: 32),

                          // 专家大头像
                          Center(
                            child: Container(
                              width: 160,
                              height: 160,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                boxShadow: [
                                  BoxShadow(
                                    color: _getCategoryColor(expert.category).withOpacity(0.4),
                                    blurRadius: 30,
                                    offset: const Offset(0, 12),
                                  ),
                                ],
                              ),
                              child: ClipOval(
                                child: Image.asset(
                                  expert.avatarPath,
                                  fit: BoxFit.cover,
                                  errorBuilder: (context, error, stackTrace) {
                                    return Container(
                                      decoration: BoxDecoration(
                                        shape: BoxShape.circle,
                                        gradient: LinearGradient(
                                          colors: [
                                            _getCategoryColor(expert.category).withOpacity(0.7),
                                            _getCategoryColor(expert.category),
                                          ],
                                        ),
                                      ),
                                      child: Icon(
                                        CupertinoIcons.person_fill,
                                        size: 80,
                                        color: Colors.white,
                                      ),
                                    );
                                  },
                                ),
                              ),
                            ),
                          ),

                          const SizedBox(height: 32),

                          // 专家名称
                          Center(
                            child: Text(
                              expert.displayName,
                              style: const TextStyle(
                                fontSize: 24,
                                fontWeight: FontWeight.bold,
                                color: Color(0xFF2C3E50),
                                height: 1.2,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),

                          const SizedBox(height: 8),

                          // 专家代号
                          Center(
                            child: Text(
                              expert.codeName,
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                                color: _getCategoryColor(expert.category),
                                letterSpacing: 0.5,
                              ),
                            ),
                          ),

                          const SizedBox(height: 24),

                          // 专家描述
                          Text(
                            expert.shortDesc,
                            style: const TextStyle(
                              fontSize: 16,
                              color: Color(0xFF7F8C8D),
                              height: 1.5,
                            ),
                            textAlign: TextAlign.center,
                          ),

                          const Spacer(),

                          // 开始聊天按钮
                          Container(
                            width: double.infinity,
                            height: 56,
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: expert.hasFreeTries
                                    ? [
                                        _getCategoryColor(expert.category),
                                        _getCategoryColor(expert.category).withOpacity(0.8),
                                      ]
                                    : [
                                        const Color(0xFF95A5A6),
                                        const Color(0xFF7F8C8D),
                                      ],
                              ),
                              borderRadius: BorderRadius.circular(16),
                              boxShadow: [
                                BoxShadow(
                                  color: _getCategoryColor(expert.category).withOpacity(0.3),
                                  blurRadius: 12,
                                  offset: const Offset(0, 6),
                                ),
                              ],
                            ),
                            child: Material(
                              color: Colors.transparent,
                              child: InkWell(
                                borderRadius: BorderRadius.circular(16),
                                onTap: expert.hasFreeTries ? () => _onExpertSelected(expert) : null,
                                child: Center(
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Icon(
                                        expert.hasFreeTries
                                            ? CupertinoIcons.chat_bubble_2_fill
                                            : CupertinoIcons.lock_fill,
                                        color: Colors.white,
                                        size: 20,
                                      ),
                                      const SizedBox(width: 12),
                                      Text(
                                        expert.hasFreeTries
                                            ? 'Start Conversation'
                                            : 'No Free Tries Available',
                                        style: const TextStyle(
                                          color: Colors.white,
                                          fontSize: 18,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ).animate()
              .fadeIn(duration: 600.ms)
              .slideY(begin: 0.3, end: 0),
          ),
        );
      },
    );
  }

  // 获取分类颜色
  Color _getCategoryColor(String category) {
    switch (category) {
      case 'Fabric Care':
        return const Color(0xFF3498DB); // 蒸汽蓝
      case 'Quick Tips':
        return const Color(0xFF1ABC9C); // 青绿
      case 'Tools':
        return const Color(0xFFF1C40F); // 金黄
      case 'Prevention':
        return const Color(0xFF9B59B6); // 紫色
      case 'Garment Care':
        return const Color(0xFFE67E22); // 橙色
      default:
        return const Color(0xFF95A5A6); // 灰色
    }
  }

  // 获取剩余次数颜色
  Color _getUsageColor(IrxAiExpertEntity expert) {
    final remaining = expert.remainingFreeTries;
    if (remaining == 0) return const Color(0xFFE74C3C); // 红色
    if (remaining == 1) return const Color(0xFFF39C12); // 橙色
    return const Color(0xFF27AE60); // 绿色
  }
}


