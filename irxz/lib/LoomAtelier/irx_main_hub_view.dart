// 主页角色选择界面 - 16:9竖屏大图卡片信息流
// 遵循熨烫主题命名和差异化规则

import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';

import '../SteamCore/irx_app_state.dart';
import '../SteamCore/irx_press_actions.dart';
import '../FabricVault/irx_ai_expert_entity.dart';
import '../WeaveParts/irx_expert_card_widget.dart';
import '../WeaveParts/irx_category_filter_widget.dart';
import '../WeaveParts/irx_steam_app_bar.dart';

class IrxMainHubView extends StatefulWidget {
  const IrxMainHubView({super.key});

  @override
  State<IrxMainHubView> createState() => _IrxMainHubViewState();
}

class _IrxMainHubViewState extends State<IrxMainHubView>
    with TickerProviderStateMixin {

  // 动画控制器
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late AnimationController _cardSwitchController;

  // 筛选状态
  String _selectedCategory = 'All';
  List<IrxAiExpertEntity> _filteredExperts = [];
  int _selectedExpertIndex = 0; // 当前选中的专家索引

  // 搜索控制器
  final TextEditingController _searchController = TextEditingController();
  bool _isSearching = false;

  @override
  void initState() {
    super.initState();

    // 初始化动画控制器
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _slideController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _cardSwitchController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    // 启动进入动画
    _fadeController.forward();
    _slideController.forward();
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _slideController.dispose();
    _cardSwitchController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  // 筛选专家列表
  void _filterExperts(List<IrxAiExpertEntity> allExperts) {
    setState(() {
      if (_selectedCategory == 'All') {
        _filteredExperts = allExperts;
      } else {
        _filteredExperts = allExperts
            .where((expert) => expert.category == _selectedCategory)
            .toList();
      }

      // 如果有搜索关键词，进一步筛选
      if (_searchController.text.isNotEmpty) {
        final query = _searchController.text.toLowerCase();
        _filteredExperts = _filteredExperts.where((expert) {
          return expert.displayName.toLowerCase().contains(query) ||
                 expert.shortDesc.toLowerCase().contains(query) ||
                 expert.codeName.toLowerCase().contains(query);
        }).toList();
      }

      // 重置选中索引
      if (_selectedExpertIndex >= _filteredExperts.length) {
        _selectedExpertIndex = 0;
      }
    });
  }

  // 选择专家
  void _selectExpert(int index) {
    if (index != _selectedExpertIndex && index < _filteredExperts.length) {
      setState(() {
        _selectedExpertIndex = index;
      });

      // 触发卡片切换动画
      _cardSwitchController.forward().then((_) {
        _cardSwitchController.reverse();
      });

      // 触觉反馈
      HapticFeedback.selectionClick();
    }
  }

  // 处理专家选择
  void _onExpertSelected(IrxAiExpertEntity expert) {
    // 触发选择动画
    HapticFeedback.lightImpact();

    // 更新Redux状态
    StoreProvider.of<IrxAppState>(context).dispatch(
      SelectAiExpertAction(expert),
    );

    // 导航到聊天页面
    Navigator.pushNamed(
      context,
      '/chat',
      arguments: expert,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5), // 浅灰背景
      body: StoreConnector<IrxAppState, List<IrxAiExpertEntity>>(
        converter: (store) => store.state.aiExperts,
        onInit: (store) {
          // 初始化时设置筛选列表
          _filteredExperts = store.state.aiExperts;
        },
        onWillChange: (oldExperts, newExperts) {
          // 当专家列表变化时重新筛选
          if (newExperts != null) {
            _filterExperts(newExperts);
          }
        },
        builder: (context, experts) {
          if (_filteredExperts.isEmpty) {
            return const Center(
              child: CircularProgressIndicator(
                color: Color(0xFF3498DB),
              ),
            );
          }

          final selectedExpert = _filteredExperts.isNotEmpty
              ? _filteredExperts[_selectedExpertIndex]
              : null;

          return Column(
            children: [
              // 固定的AppBar
              IrxSteamAppBar(
                title: 'Ironing Experts',
                isSearching: _isSearching,
                searchController: _searchController,
                onSearchToggle: () {
                  setState(() {
                    _isSearching = !_isSearching;
                    if (!_isSearching) {
                      _searchController.clear();
                      _filterExperts(experts);
                    }
                  });
                },
                onSearchChanged: (query) {
                  _filterExperts(experts);
                },
              ),

              // 可滚动内容
              Expanded(
                child: CustomScrollView(
                  physics: const BouncingScrollPhysics(),
                  slivers: [

              // 分类筛选器
              SliverToBoxAdapter(
                child: IrxCategoryFilterWidget(
                  selectedCategory: _selectedCategory,
                  onCategoryChanged: (category) {
                    setState(() {
                      _selectedCategory = category;
                    });
                    _filterExperts(experts);
                  },
                ).animate()
                  .fadeIn(duration: 600.ms, delay: 200.ms)
                  .slideY(begin: -0.3, end: 0),
              ),

              // 专家头像选择器
              SliverToBoxAdapter(
                child: _buildExpertAvatarSelector(),
              ),

              // 选中专家的大卡片
              if (selectedExpert != null)
                SliverToBoxAdapter(
                  child: _buildSelectedExpertCard(selectedExpert),
                ),

                    // 底部间距
                    const SliverToBoxAdapter(
                      child: SizedBox(height: 100),
                    ),
                  ],
                ),
              ),
            ],
          );
        },
      ),
      
      // 浮动操作按钮 - 快速访问待办清单
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () {
          Navigator.pushNamed(context, '/tasks');
        },
        backgroundColor: const Color(0xFF3498DB), // 蒸汽蓝
        foregroundColor: Colors.white,
        icon: const Icon(CupertinoIcons.list_bullet),
        label: const Text(
          'Quick Tasks',
          style: TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 14,
          ),
        ),
      ).animate()
        .scale(delay: 1000.ms, duration: 400.ms)
        .shimmer(delay: 1200.ms, duration: 1000.ms),
    );
  }

  // 构建专家头像选择器
  Widget _buildExpertAvatarSelector() {
    return Container(
      height: 130, // 进一步增加高度避免截取
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Text(
              'Select Expert',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Color(0xFF2C3E50),
              ),
            ),
          ),

          const SizedBox(height: 12),

          // 头像列表
          Expanded(
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: _filteredExperts.length,
              itemBuilder: (context, index) {
                final expert = _filteredExperts[index];
                final isSelected = index == _selectedExpertIndex;

                return Padding(
                  padding: const EdgeInsets.only(right: 16, top: 8, bottom: 8),
                  child: GestureDetector(
                    onTap: () => _selectExpert(index),
                    child: AnimatedContainer(
                      duration: const Duration(milliseconds: 300),
                      curve: Curves.easeInOut,
                      child: Column(
                        children: [
                          // 头像容器
                          Container(
                            width: 64,
                            height: 64,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              border: Border.all(
                                color: isSelected
                                    ? _getCategoryColor(expert.category)
                                    : Colors.transparent,
                                width: isSelected ? 3 : 0,
                              ),
                              boxShadow: isSelected ? [
                                BoxShadow(
                                  color: _getCategoryColor(expert.category).withOpacity(0.3),
                                  blurRadius: 12,
                                  offset: const Offset(0, 4),
                                ),
                              ] : null,
                            ),
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(30), // 确保圆形裁剪
                              child: Image.asset(
                                expert.avatarPath,
                                width: 60,
                                height: 60,
                                fit: BoxFit.cover,
                                errorBuilder: (context, error, stackTrace) {
                                  return Container(
                                    width: 60,
                                    height: 60,
                                    decoration: BoxDecoration(
                                      shape: BoxShape.circle,
                                      gradient: LinearGradient(
                                        colors: [
                                          _getCategoryColor(expert.category).withOpacity(0.7),
                                          _getCategoryColor(expert.category),
                                        ],
                                      ),
                                    ),
                                    child: Icon(
                                      CupertinoIcons.person_fill,
                                      size: 30,
                                      color: Colors.white,
                                    ),
                                  );
                                },
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ).animate()
                    .scale(
                      duration: 200.ms,
                      begin: const Offset(1.0, 1.0),
                      end: isSelected ? const Offset(1.1, 1.1) : const Offset(1.0, 1.0),
                    ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  // 构建选中专家的大卡片
  Widget _buildSelectedExpertCard(IrxAiExpertEntity expert) {
    return AnimatedBuilder(
      animation: _cardSwitchController,
      builder: (context, child) {
        return Transform.scale(
          scale: 1.0 - (_cardSwitchController.value * 0.05),
          child: Opacity(
            opacity: 1.0 - (_cardSwitchController.value * 0.3),
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 20),
              height: 480, // 缩小卡片高度
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(24),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.25),
                    blurRadius: 30,
                    offset: const Offset(0, 12),
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(24),
                child: Stack(
                  fit: StackFit.expand,
                  children: [
                    // 背景图片
                    Image.asset(
                      expert.avatarPath,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                              colors: [
                                _getCategoryColor(expert.category).withOpacity(0.7),
                                _getCategoryColor(expert.category),
                              ],
                            ),
                          ),
                          child: Center(
                            child: Icon(
                              CupertinoIcons.person_fill,
                              size: 120,
                              color: Colors.white.withOpacity(0.3),
                            ),
                          ),
                        );
                      },
                    ),

                    // 渐变遮罩层
                    Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [
                            Colors.black.withOpacity(0.3),
                            Colors.black.withOpacity(0.1),
                            Colors.black.withOpacity(0.7),
                          ],
                          stops: const [0.0, 0.5, 1.0],
                        ),
                      ),
                    ),

                    // 顶部信息栏
                    Positioned(
                      top: 32,
                      left: 32,
                      right: 32,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          // 分类标签
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 8,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.white.withOpacity(0.2),
                              borderRadius: BorderRadius.circular(20),
                              border: Border.all(
                                color: Colors.white.withOpacity(0.3),
                                width: 1,
                              ),
                            ),
                            child: Text(
                              expert.category,
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),

                          // 详情按钮
                          GestureDetector(
                            onTap: () {
                              // TODO: 显示专家详情
                              HapticFeedback.lightImpact();
                            },
                            child: Icon(
                              CupertinoIcons.info_circle,
                              color: Colors.white.withOpacity(0.8),
                              size: 24,
                            ),
                          ),
                        ],
                      ),
                    ),

                    // 底部布局 - 左下角信息 + 右下角按钮
                    Positioned(
                            left: 12,
                            right: 12,
                            bottom: 12,
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.end,
                              children: [
                                // 左下角 - 专家信息
                                Expanded(
                                  child: Container(
                                    padding: const EdgeInsets.all(16),
                                    decoration: BoxDecoration(
                                      color: Colors.black.withOpacity(0.2),
                                      borderRadius: BorderRadius.circular(20),
                                    ),
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        // 专家名称
                                        Text(
                                          expert.displayName,
                                          style: const TextStyle(
                                            fontSize: 20,
                                            fontWeight: FontWeight.bold,
                                            color: Colors.white,
                                            height: 1.2,
                                          ),
                                          maxLines: 2,
                                          overflow: TextOverflow.ellipsis,
                                        ),

                                        const SizedBox(height: 6),

                                        // // 专家代号
                                        // Text(
                                        //   expert.codeName,
                                        //   style: TextStyle(
                                        //     fontSize: 14,
                                        //     fontWeight: FontWeight.w500,
                                        //     color: _getCategoryColor(expert.category),
                                        //     letterSpacing: 0.5,
                                        //   ),
                                        // ),
                                        //
                                        // const SizedBox(height: 12),

                                        // 专家描述
                                        Text(
                                          expert.shortDesc,
                                          style: TextStyle(
                                            fontSize: 14,
                                            color: _getCategoryColor(expert.category),
                                            height: 1.4,
                                          ),
                                          maxLines: 3,
                                          overflow: TextOverflow.ellipsis,
                                        ),


                                      ],
                                    ),
                                  ),
                                ),

                                const SizedBox(width: 8),

                                // 右下角 - 聊天按钮
                                GestureDetector(
                                  onTap: expert.hasFreeTries ? () => _onExpertSelected(expert) : null,
                                  child: Container(
                                    width: 48,
                                    height: 48,
                                    decoration: BoxDecoration(
                                      gradient: LinearGradient(
                                        colors: expert.hasFreeTries
                                            ? [
                                                Colors.white,
                                                Colors.white.withOpacity(0.9),
                                              ]
                                            : [
                                                const Color(0xFF95A5A6),
                                                const Color(0xFF7F8C8D),
                                              ],
                                      ),
                                      shape: BoxShape.circle,
                                      boxShadow: [
                                        BoxShadow(
                                          color: Colors.black.withOpacity(0.3),
                                          blurRadius: 12,
                                          offset: const Offset(0, 6),
                                        ),
                                      ],
                                    ),
                                    child: Icon(
                                      expert.hasFreeTries
                                          ? CupertinoIcons.chat_bubble_2_fill
                                          : CupertinoIcons.lock_fill,
                                      color: expert.hasFreeTries
                                          ? _getCategoryColor(expert.category)
                                          : Colors.white,
                                      size: 24,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                  ],
                ),
              ),
            ).animate()
              .fadeIn(duration: 600.ms)
              .slideY(begin: 0.3, end: 0),
          ),
        );
      },
    );
  }

  // 获取分类颜色
  Color _getCategoryColor(String category) {
    switch (category) {
      case 'Fabric Care':
        return const Color(0xFF3498DB); // 蒸汽蓝
      case 'Quick Tips':
        return const Color(0xFF1ABC9C); // 青绿
      case 'Tools':
        return const Color(0xFFF1C40F); // 金黄
      case 'Prevention':
        return const Color(0xFF9B59B6); // 紫色
      case 'Garment Care':
        return const Color(0xFFE67E22); // 橙色
      default:
        return const Color(0xFF95A5A6); // 灰色
    }
  }

  // 获取剩余次数颜色
  Color _getUsageColor(IrxAiExpertEntity expert) {
    final remaining = expert.remainingFreeTries;
    if (remaining == 0) return const Color(0xFFE74C3C); // 红色
    if (remaining == 1) return const Color(0xFFF39C12); // 橙色
    return const Color(0xFF27AE60); // 绿色
  }
}


