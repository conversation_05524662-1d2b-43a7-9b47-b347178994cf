// Irxz主应用入口 - 家庭衣物熨烫简易指导师
// 遵循熨烫主题命名和差异化规则

import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:flutter_redux/flutter_redux.dart';

import 'SteamCore/irx_app_state.dart';
import 'SteamCore/irx_store_config.dart';
import 'LoomAtelier/irx_main_hub_view.dart';
import 'ThreadRoutes/irx_route_config.dart';

void main() {
  // 确保Flutter绑定初始化
  WidgetsFlutterBinding.ensureInitialized();

  // 设置系统UI样式
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.dark,
      systemNavigationBarColor: Colors.white,
      systemNavigationBarIconBrightness: Brightness.dark,
    ),
  );

  // 设置首选方向为竖屏
  SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  runApp(const IrxApp());
}

class IrxApp extends StatelessWidget {
  const IrxApp({super.key});

  @override
  Widget build(BuildContext context) {
    return StoreProvider<IrxAppState>(
      store: IrxStoreConfig.store,
      child: MaterialApp(
        title: 'Irxz - Ironing Expert',
        debugShowCheckedModeBanner: false,

        // 应用主题配置
        theme: _buildAppTheme(),

        // 路由配置
        initialRoute: '/',
        onGenerateRoute: IrxRouteConfig.generateRoute,

        // 主页
        home: const IrxMainHubView(),
      ),
    );
  }

  // 构建应用主题
  ThemeData _buildAppTheme() {
    return ThemeData(
      // 使用Material 3设计
      useMaterial3: true,

      // 主色调配置 - 蒸汽蓝主题
      colorScheme: ColorScheme.fromSeed(
        seedColor: const Color(0xFF3498DB), // 蒸汽蓝
        brightness: Brightness.light,
      ),

      // 字体配置
      fontFamily: 'SF Pro Display', // iOS风格字体

      // AppBar主题
      appBarTheme: const AppBarTheme(
        backgroundColor: Colors.transparent,
        elevation: 0,
        systemOverlayStyle: SystemUiOverlayStyle.dark,
        titleTextStyle: TextStyle(
          color: Color(0xFF2C3E50),
          fontSize: 20,
          fontWeight: FontWeight.w600,
        ),
        iconTheme: IconThemeData(
          color: Color(0xFF3498DB),
        ),
      ),

      // 卡片主题
      cardTheme: CardTheme(
        elevation: 8,
        shadowColor: Colors.black.withOpacity(0.1),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
      ),

      // 按钮主题
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFF3498DB),
          foregroundColor: Colors.white,
          elevation: 4,
          shadowColor: const Color(0xFF3498DB).withOpacity(0.3),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          padding: const EdgeInsets.symmetric(
            horizontal: 24,
            vertical: 12,
          ),
        ),
      ),

      // 输入框主题
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: Colors.white,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: const Color(0xFF3498DB).withOpacity(0.3),
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: const Color(0xFF3498DB).withOpacity(0.3),
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(
            color: Color(0xFF3498DB),
            width: 2,
          ),
        ),
      ),

      // 文本主题
      textTheme: const TextTheme(
        headlineLarge: TextStyle(
          color: Color(0xFF2C3E50),
          fontSize: 32,
          fontWeight: FontWeight.bold,
        ),
        headlineMedium: TextStyle(
          color: Color(0xFF2C3E50),
          fontSize: 24,
          fontWeight: FontWeight.w600,
        ),
        bodyLarge: TextStyle(
          color: Color(0xFF2C3E50),
          fontSize: 16,
          height: 1.5,
        ),
        bodyMedium: TextStyle(
          color: Color(0xFF7F8C8D),
          fontSize: 14,
          height: 1.4,
        ),
      ),
    );
  }
}


