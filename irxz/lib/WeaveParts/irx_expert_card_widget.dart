// AI专家卡片组件 - 16:9竖屏大图个性化卡片
// 遵循熨烫主题命名和差异化规则

import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:shimmer/shimmer.dart';

import '../FabricVault/irx_ai_expert_entity.dart';

class IrxExpertCardWidget extends StatefulWidget {
  final IrxAiExpertEntity expert;
  final VoidCallback onTap;
  final int cardIndex;

  const IrxExpertCardWidget({
    super.key,
    required this.expert,
    required this.onTap,
    required this.cardIndex,
  });

  @override
  State<IrxExpertCardWidget> createState() => _IrxExpertCardWidgetState();
}

class _IrxExpertCardWidgetState extends State<IrxExpertCardWidget>
    with SingleTickerProviderStateMixin {
  
  late AnimationController _hoverController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _shadowAnimation;
  
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    
    _hoverController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.98,
    ).animate(CurvedAnimation(
      parent: _hoverController,
      curve: Curves.easeInOut,
    ));
    
    _shadowAnimation = Tween<double>(
      begin: 1.0,
      end: 1.5,
    ).animate(CurvedAnimation(
      parent: _hoverController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _hoverController.dispose();
    super.dispose();
  }

  // 获取分类颜色
  Color _getCategoryColor() {
    switch (widget.expert.category) {
      case 'Fabric Care':
        return const Color(0xFF3498DB); // 蒸汽蓝
      case 'Quick Tips':
        return const Color(0xFF1ABC9C); // 青绿
      case 'Tools':
        return const Color(0xFFF1C40F); // 金黄
      case 'Prevention':
        return const Color(0xFF9B59B6); // 紫色
      case 'Garment Care':
        return const Color(0xFFE67E22); // 橙色
      default:
        return const Color(0xFF95A5A6); // 灰色
    }
  }

  // 获取剩余次数颜色
  Color _getUsageColor() {
    final remaining = widget.expert.remainingFreeTries;
    if (remaining == 0) return const Color(0xFFE74C3C); // 红色
    if (remaining == 1) return const Color(0xFFF39C12); // 橙色
    return const Color(0xFF27AE60); // 绿色
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _hoverController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: GestureDetector(
            onTapDown: (_) {
              setState(() => _isPressed = true);
              _hoverController.forward();
            },
            onTapUp: (_) {
              setState(() => _isPressed = false);
              _hoverController.reverse();
              widget.onTap();
            },
            onTapCancel: () {
              setState(() => _isPressed = false);
              _hoverController.reverse();
            },
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1 * _shadowAnimation.value),
                    blurRadius: 20 * _shadowAnimation.value,
                    offset: Offset(0, 8 * _shadowAnimation.value),
                  ),
                  BoxShadow(
                    color: _getCategoryColor().withOpacity(0.1),
                    blurRadius: 30,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(20),
                child: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        Colors.white,
                        Colors.white.withOpacity(0.95),
                      ],
                    ),
                  ),
                  child: Stack(
                    children: [
                      // 背景装饰
                      Positioned(
                        top: -50,
                        right: -50,
                        child: Container(
                          width: 150,
                          height: 150,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: _getCategoryColor().withOpacity(0.05),
                          ),
                        ),
                      ),
                      
                      // 主要内容
                      Padding(
                        padding: const EdgeInsets.all(24),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // 顶部状态栏
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                // 分类标签
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 12,
                                    vertical: 6,
                                  ),
                                  decoration: BoxDecoration(
                                    color: _getCategoryColor().withOpacity(0.1),
                                    borderRadius: BorderRadius.circular(20),
                                    border: Border.all(
                                      color: _getCategoryColor().withOpacity(0.3),
                                      width: 1,
                                    ),
                                  ),
                                  child: Text(
                                    widget.expert.category,
                                    style: TextStyle(
                                      color: _getCategoryColor(),
                                      fontSize: 12,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ),
                                
                                // 剩余次数指示器
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 10,
                                    vertical: 4,
                                  ),
                                  decoration: BoxDecoration(
                                    color: _getUsageColor().withOpacity(0.1),
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Icon(
                                        CupertinoIcons.chat_bubble_fill,
                                        size: 12,
                                        color: _getUsageColor(),
                                      ),
                                      const SizedBox(width: 4),
                                      Text(
                                        '${widget.expert.remainingFreeTries}',
                                        style: TextStyle(
                                          color: _getUsageColor(),
                                          fontSize: 12,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                            
                            const SizedBox(height: 24),
                            
                            // 专家头像
                            Center(
                              child: Container(
                                width: 120,
                                height: 120,
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  boxShadow: [
                                    BoxShadow(
                                      color: _getCategoryColor().withOpacity(0.3),
                                      blurRadius: 20,
                                      offset: const Offset(0, 8),
                                    ),
                                  ],
                                ),
                                child: ClipOval(
                                  child: Image.asset(
                                    widget.expert.avatarPath,
                                    fit: BoxFit.cover,
                                    errorBuilder: (context, error, stackTrace) {
                                      return Container(
                                        decoration: BoxDecoration(
                                          shape: BoxShape.circle,
                                          gradient: LinearGradient(
                                            colors: [
                                              _getCategoryColor().withOpacity(0.7),
                                              _getCategoryColor(),
                                            ],
                                          ),
                                        ),
                                        child: Icon(
                                          CupertinoIcons.person_fill,
                                          size: 60,
                                          color: Colors.white,
                                        ),
                                      );
                                    },
                                  ),
                                ),
                              ),
                            ),
                            
                            const SizedBox(height: 24),
                            
                            // 专家名称
                            Text(
                              widget.expert.displayName,
                              style: const TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                                color: Color(0xFF2C3E50),
                                height: 1.2,
                              ),
                              textAlign: TextAlign.center,
                            ),
                            
                            const SizedBox(height: 12),
                            
                            // 专家代号
                            Center(
                              child: Text(
                                widget.expert.codeName,
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                  color: _getCategoryColor(),
                                  letterSpacing: 0.5,
                                ),
                              ),
                            ),
                            
                            const SizedBox(height: 20),
                            
                            // 专家描述
                            Expanded(
                              child: Text(
                                widget.expert.shortDesc,
                                style: const TextStyle(
                                  fontSize: 14,
                                  color: Color(0xFF7F8C8D),
                                  height: 1.4,
                                ),
                                textAlign: TextAlign.center,
                                maxLines: 3,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            
                            const SizedBox(height: 20),
                            
                            // 底部操作按钮
                            Container(
                              width: double.infinity,
                              height: 48,
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  colors: widget.expert.hasFreeTries
                                      ? [
                                          _getCategoryColor(),
                                          _getCategoryColor().withOpacity(0.8),
                                        ]
                                      : [
                                          const Color(0xFF95A5A6),
                                          const Color(0xFF7F8C8D),
                                        ],
                                ),
                                borderRadius: BorderRadius.circular(12),
                                boxShadow: [
                                  BoxShadow(
                                    color: _getCategoryColor().withOpacity(0.3),
                                    blurRadius: 8,
                                    offset: const Offset(0, 4),
                                  ),
                                ],
                              ),
                              child: Material(
                                color: Colors.transparent,
                                child: InkWell(
                                  borderRadius: BorderRadius.circular(12),
                                  onTap: widget.expert.hasFreeTries ? widget.onTap : null,
                                  child: Center(
                                    child: Row(
                                      mainAxisAlignment: MainAxisAlignment.center,
                                      children: [
                                        Icon(
                                          widget.expert.hasFreeTries
                                              ? CupertinoIcons.chat_bubble_2_fill
                                              : CupertinoIcons.lock_fill,
                                          color: Colors.white,
                                          size: 18,
                                        ),
                                        const SizedBox(width: 8),
                                        Text(
                                          widget.expert.hasFreeTries
                                              ? 'Start Chat'
                                              : 'No Free Tries',
                                          style: const TextStyle(
                                            color: Colors.white,
                                            fontSize: 16,
                                            fontWeight: FontWeight.w600,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      
                      // 选中时的闪烁效果
                      if (_isPressed)
                        Positioned.fill(
                          child: Container(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(20),
                              color: _getCategoryColor().withOpacity(0.1),
                            ),
                          ).animate(onPlay: (controller) => controller.repeat())
                            .shimmer(duration: 1000.ms),
                        ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
