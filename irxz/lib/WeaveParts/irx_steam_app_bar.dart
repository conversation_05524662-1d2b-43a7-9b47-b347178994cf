// 自定义AppBar组件 - 蒸汽主题应用栏
// 遵循熨烫主题命名和差异化规则

import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:flutter_animate/flutter_animate.dart';

class IrxSteamAppBar extends StatefulWidget {
  final String title;
  final bool isSearching;
  final TextEditingController searchController;
  final VoidCallback onSearchToggle;
  final Function(String) onSearchChanged;

  const IrxSteamAppBar({
    super.key,
    required this.title,
    required this.isSearching,
    required this.searchController,
    required this.onSearchToggle,
    required this.onSearchChanged,
  });

  @override
  State<IrxSteamAppBar> createState() => _IrxSteamAppBarState();
}

class _IrxSteamAppBarState extends State<IrxSteamAppBar>
    with SingleTickerProviderStateMixin {
  
  late AnimationController _searchController;
  late Animation<double> _searchAnimation;
  
  @override
  void initState() {
    super.initState();
    
    _searchController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _searchAnimation = CurvedAnimation(
      parent: _searchController,
      curve: Curves.easeInOut,
    );
  }

  @override
  void didUpdateWidget(IrxSteamAppBar oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (widget.isSearching != oldWidget.isSearching) {
      if (widget.isSearching) {
        _searchController.forward();
      } else {
        _searchController.reverse();
      }
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 100,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            const Color(0xFF3498DB).withOpacity(0.1), // 蒸汽蓝
            const Color(0xFF1ABC9C).withOpacity(0.05), // 青绿
          ],
        ),
      ),
      child: Stack(
        children: [
          // 背景装饰
          Positioned(
            top: -30,
            right: -30,
            child: Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: const Color(0xFF3498DB).withOpacity(0.1),
              ),
            ),
          ),

          Positioned(
            top: 40,
            left: -20,
            child: Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: const Color(0xFF1ABC9C).withOpacity(0.08),
              ),
            ),
          ),

          // 主要内容
          SafeArea(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min, // 防止溢出
                children: [
                  const SizedBox(height: 4), // 进一步减少间距

                  // 顶部操作栏
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // 应用图标和标题
                      Row(
                        children: [
                          Container(
                            width: 40,
                            height: 40,
                            decoration: BoxDecoration(
                              gradient: const LinearGradient(
                                colors: [
                                  Color(0xFF3498DB),
                                  Color(0xFF1ABC9C),
                                ],
                              ),
                              borderRadius: BorderRadius.circular(12),
                              boxShadow: [
                                BoxShadow(
                                  color: const Color(0xFF3498DB).withOpacity(0.3),
                                  blurRadius: 8,
                                  offset: const Offset(0, 4),
                                ),
                              ],
                            ),
                            child: const Icon(
                              CupertinoIcons.sparkles,
                              color: Colors.white,
                              size: 20,
                            ),
                          ),

                          const SizedBox(width: 12),

                          AnimatedSwitcher(
                            duration: const Duration(milliseconds: 300),
                            child: widget.isSearching
                                ? const SizedBox.shrink()
                                : Text(
                                    widget.title,
                                    key: const ValueKey('title'),
                                    style: const TextStyle(
                                      fontSize: 24,
                                      fontWeight: FontWeight.bold,
                                      color: Color(0xFF2C3E50),
                                    ),
                                  ),
                          ),
                        ],
                      ),

                      // 搜索按钮
                      Container(
                        width: 44,
                        height: 44,
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.9),
                          borderRadius: BorderRadius.circular(12),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.1),
                              blurRadius: 8,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Material(
                          color: Colors.transparent,
                          child: InkWell(
                            borderRadius: BorderRadius.circular(12),
                            onTap: widget.onSearchToggle,
                            child: Icon(
                              widget.isSearching
                                  ? CupertinoIcons.xmark
                                  : CupertinoIcons.search,
                              color: const Color(0xFF3498DB),
                              size: 20,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 4), // 进一步减少间距

                  // 搜索框
                  Flexible( // 使用Flexible防止溢出
                    child: AnimatedBuilder(
                    animation: _searchAnimation,
                    builder: (context, child) {
                      return Transform.translate(
                        offset: Offset(0, (1 - _searchAnimation.value) * -20),
                        child: Opacity(
                          opacity: _searchAnimation.value,
                          child: widget.isSearching
                              ? Container(
                                  height: 48,
                                  decoration: BoxDecoration(
                                    color: Colors.white.withOpacity(0.9),
                                    borderRadius: BorderRadius.circular(12),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.black.withOpacity(0.1),
                                        blurRadius: 8,
                                        offset: const Offset(0, 2),
                                      ),
                                    ],
                                  ),
                                  child: TextField(
                                    controller: widget.searchController,
                                    onChanged: widget.onSearchChanged,
                                    autofocus: true,
                                    decoration: InputDecoration(
                                      hintText: 'Search experts...',
                                      hintStyle: TextStyle(
                                        color: const Color(0xFF7F8C8D),
                                        fontSize: 16,
                                      ),
                                      prefixIcon: Icon(
                                        CupertinoIcons.search,
                                        color: const Color(0xFF3498DB),
                                        size: 20,
                                      ),
                                      border: InputBorder.none,
                                      contentPadding: const EdgeInsets.symmetric(
                                        horizontal: 16,
                                        vertical: 12,
                                      ),
                                    ),
                                    style: const TextStyle(
                                      fontSize: 16,
                                      color: Color(0xFF2C3E50),
                                    ),
                                  ),
                                )
                              : const SizedBox.shrink(),
                        ),
                      );
                    },
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
