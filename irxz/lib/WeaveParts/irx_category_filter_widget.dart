// 分类筛选组件 - 专家分类标签筛选器
// 遵循熨烫主题命名和差异化规则

import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_animate/flutter_animate.dart';

class IrxCategoryFilterWidget extends StatefulWidget {
  final String selectedCategory;
  final Function(String) onCategoryChanged;

  const IrxCategoryFilterWidget({
    super.key,
    required this.selectedCategory,
    required this.onCategoryChanged,
  });

  @override
  State<IrxCategoryFilterWidget> createState() => _IrxCategoryFilterWidgetState();
}

class _IrxCategoryFilterWidgetState extends State<IrxCategoryFilterWidget>
    with SingleTickerProviderStateMixin {
  
  late AnimationController _selectionController;
  late Animation<double> _selectionAnimation;
  
  // 分类列表配置
  final List<CategoryItem> _categories = [
    CategoryItem(
      name: 'All',
      icon: CupertinoIcons.grid,
      color: Color(0xFF95A5A6),
      count: 10,
    ),
    CategoryItem(
      name: 'Fabric Care',
      icon: CupertinoIcons.textformat,
      color: Color(0xFF3498DB), // 蒸汽蓝
      count: 4,
    ),
    CategoryItem(
      name: 'Quick Tips',
      icon: CupertinoIcons.bolt_fill,
      color: Color(0xFF1ABC9C), // 青绿
      count: 2,
    ),
    CategoryItem(
      name: 'Tools',
      icon: CupertinoIcons.wrench_fill,
      color: Color(0xFFF1C40F), // 金黄
      count: 2,
    ),
    CategoryItem(
      name: 'Garment Care',
      icon: CupertinoIcons.tag_fill,
      color: Color(0xFFE67E22), // 橙色
      count: 1,
    ),
    CategoryItem(
      name: 'Prevention',
      icon: CupertinoIcons.shield_fill,
      color: Color(0xFF9B59B6), // 紫色
      count: 1,
    ),
  ];

  @override
  void initState() {
    super.initState();
    
    _selectionController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _selectionAnimation = CurvedAnimation(
      parent: _selectionController,
      curve: Curves.elasticOut,
    );
  }

  @override
  void dispose() {
    _selectionController.dispose();
    super.dispose();
  }

  void _onCategoryTap(String category) {
    if (category != widget.selectedCategory) {
      _selectionController.forward().then((_) {
        _selectionController.reverse();
      });
      
      widget.onCategoryChanged(category);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 120,
      padding: const EdgeInsets.symmetric(vertical: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Text(
              'Expert Categories',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Color(0xFF2C3E50),
              ),
            ),
          ),
          
          const SizedBox(height: 12),
          
          // 分类标签列表
          Expanded(
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: _categories.length,
              itemBuilder: (context, index) {
                final category = _categories[index];
                final isSelected = category.name == widget.selectedCategory;
                
                return Padding(
                  padding: const EdgeInsets.only(right: 12),
                  child: AnimatedBuilder(
                    animation: _selectionAnimation,
                    builder: (context, child) {
                      final scale = isSelected && _selectionAnimation.value > 0
                          ? 1.0 + (_selectionAnimation.value * 0.1)
                          : 1.0;
                      
                      return Transform.scale(
                        scale: scale,
                        child: GestureDetector(
                          onTap: () => _onCategoryTap(category.name),
                          child: AnimatedContainer(
                            duration: const Duration(milliseconds: 200),
                            curve: Curves.easeInOut,
                            padding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 8,
                            ),
                            decoration: BoxDecoration(
                              color: isSelected
                                  ? category.color
                                  : category.color.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(20),
                              border: Border.all(
                                color: isSelected
                                    ? category.color
                                    : category.color.withOpacity(0.3),
                                width: isSelected ? 2 : 1,
                              ),
                              boxShadow: isSelected
                                  ? [
                                      BoxShadow(
                                        color: category.color.withOpacity(0.3),
                                        blurRadius: 8,
                                        offset: const Offset(0, 4),
                                      ),
                                    ]
                                  : null,
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                // 分类图标
                                Icon(
                                  category.icon,
                                  size: 18,
                                  color: isSelected
                                      ? Colors.white
                                      : category.color,
                                ),
                                
                                const SizedBox(width: 8),
                                
                                // 分类名称
                                Text(
                                  category.name,
                                  style: TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w600,
                                    color: isSelected
                                        ? Colors.white
                                        : category.color,
                                  ),
                                ),
                                
                                const SizedBox(width: 6),
                                
                                // 数量标识
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 6,
                                    vertical: 2,
                                  ),
                                  decoration: BoxDecoration(
                                    color: isSelected
                                        ? Colors.white.withOpacity(0.2)
                                        : category.color.withOpacity(0.2),
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                  child: Text(
                                    '${category.count}',
                                    style: TextStyle(
                                      fontSize: 12,
                                      fontWeight: FontWeight.bold,
                                      color: isSelected
                                          ? Colors.white
                                          : category.color,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}

// 分类项数据模型
class CategoryItem {
  final String name;
  final IconData icon;
  final Color color;
  final int count;

  const CategoryItem({
    required this.name,
    required this.icon,
    required this.color,
    required this.count,
  });
}
