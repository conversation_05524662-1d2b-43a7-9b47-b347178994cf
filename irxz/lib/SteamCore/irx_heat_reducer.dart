// Redux Reducer - 状态转换逻辑处理
// 遵循熨烫主题命名和差异化规则

import '../FabricVault/irx_ai_expert_entity.dart';
import '../FabricVault/irx_chat_record_entity.dart';
import '../FabricVault/irx_task_item_entity.dart';
import '../FabricVault/irx_user_profile_entity.dart';
import 'irx_app_state.dart';
import 'irx_press_actions.dart';

// 主Reducer - 处理所有状态变化
IrxAppState irxAppReducer(IrxAppState state, dynamic action) {
  return IrxAppState(
    // AI专家状态处理
    aiExperts: _aiExpertsReducer(state.aiExperts, action),
    selectedExpert: _selectedExpertReducer(state.selectedExpert, action),
    
    // 聊天对话状态处理
    chatRecords: _chatRecordsReducer(state.chatRecords, action),
    isChatLoading: _chatLoadingReducer(state.isChatLoading, action),
    chatError: _chatErrorReducer(state.chatError, action),
    
    // 待办任务状态处理
    taskItems: _taskItemsReducer(state.taskItems, action),
    isTaskLoading: _taskLoadingReducer(state.isTaskLoading, action),
    
    // 用户信息状态处理
    userProfile: _userProfileReducer(state.userProfile, action),
    isUserLoading: _userLoadingReducer(state.isUserLoading, action),
    
    // 应用通用状态处理
    isInitialized: _appInitializedReducer(state.isInitialized, action),
    globalError: _globalErrorReducer(state.globalError, action),
    isNetworkConnected: _networkStatusReducer(state.isNetworkConnected, action),
  );
}

// ============ AI专家相关Reducers ============

List<IrxAiExpertEntity> _aiExpertsReducer(List<IrxAiExpertEntity> experts, dynamic action) {
  if (action is InitAiExpertsAction) {
    return action.experts;
  }
  
  if (action is UpdateExpertUsageAction) {
    return experts.map((expert) {
      if (expert.id == action.expertId) {
        return expert.copyWith(
          usedCount: action.usedCount,
          updatedAt: DateTime.now(),
        );
      }
      return expert;
    }).toList();
  }
  
  return experts;
}

IrxAiExpertEntity? _selectedExpertReducer(IrxAiExpertEntity? selectedExpert, dynamic action) {
  if (action is SelectAiExpertAction) {
    return action.expert;
  }
  
  return selectedExpert;
}

// ============ 聊天对话相关Reducers ============

List<IrxChatRecordEntity> _chatRecordsReducer(List<IrxChatRecordEntity> records, dynamic action) {
  if (action is AddChatRecordAction) {
    return [...records, action.record];
  }
  
  if (action is LoadChatHistoryAction) {
    return action.records;
  }
  
  if (action is DeleteChatRecordAction) {
    return records.where((record) => record.id != action.recordId).toList();
  }
  
  if (action is ClearChatHistoryAction) {
    if (action.expertId != null) {
      // 清空特定专家的聊天记录
      return records.where((record) => record.expertId != action.expertId).toList();
    } else {
      // 清空所有聊天记录
      return [];
    }
  }
  
  return records;
}

bool _chatLoadingReducer(bool isLoading, dynamic action) {
  if (action is StartChatLoadingAction) {
    return true;
  }
  
  if (action is StopChatLoadingAction) {
    return false;
  }
  
  return isLoading;
}

String? _chatErrorReducer(String? error, dynamic action) {
  if (action is SetChatErrorAction) {
    return action.error;
  }
  
  // 清除错误状态
  if (action is StartChatLoadingAction || action is AddChatRecordAction) {
    return null;
  }
  
  return error;
}

// ============ 待办任务相关Reducers ============

List<IrxTaskItemEntity> _taskItemsReducer(List<IrxTaskItemEntity> tasks, dynamic action) {
  if (action is AddTaskItemAction) {
    return [...tasks, action.task];
  }
  
  if (action is UpdateTaskItemAction) {
    return tasks.map((task) {
      if (task.id == action.task.id) {
        return action.task;
      }
      return task;
    }).toList();
  }
  
  if (action is DeleteTaskItemAction) {
    return tasks.where((task) => task.id != action.taskId).toList();
  }
  
  if (action is ToggleTaskCompletionAction) {
    return tasks.map((task) {
      if (task.id == action.taskId) {
        return task.isCompleted ? task.markAsPending() : task.markAsCompleted();
      }
      return task;
    }).toList();
  }
  
  if (action is LoadTaskListAction) {
    return action.tasks;
  }
  
  return tasks;
}

bool _taskLoadingReducer(bool isLoading, dynamic action) {
  if (action is StartTaskLoadingAction) {
    return true;
  }
  
  if (action is StopTaskLoadingAction) {
    return false;
  }
  
  return isLoading;
}

// ============ 用户信息相关Reducers ============

IrxUserProfileEntity? _userProfileReducer(IrxUserProfileEntity? profile, dynamic action) {
  if (action is SetUserProfileAction) {
    return action.profile;
  }
  
  if (action is UpdateUserAvatarAction && profile != null) {
    return profile.copyWith(
      avatarPath: action.avatarPath,
      updatedAt: DateTime.now(),
    );
  }
  
  if (action is UpdateUserNicknameAction && profile != null) {
    return profile.copyWith(
      nickname: action.nickname,
      updatedAt: DateTime.now(),
    );
  }
  
  // 更新专家使用次数
  if (action is UpdateExpertUsageAction && profile != null) {
    return profile.incrementExpertUsage(action.expertId);
  }
  
  return profile;
}

bool _userLoadingReducer(bool isLoading, dynamic action) {
  if (action is StartUserLoadingAction) {
    return true;
  }
  
  if (action is StopUserLoadingAction) {
    return false;
  }
  
  return isLoading;
}

// ============ 应用通用相关Reducers ============

bool _appInitializedReducer(bool isInitialized, dynamic action) {
  if (action is SetAppInitializedAction) {
    return action.isInitialized;
  }
  
  return isInitialized;
}

String? _globalErrorReducer(String? error, dynamic action) {
  if (action is SetGlobalErrorAction) {
    return action.error;
  }
  
  return error;
}

bool _networkStatusReducer(bool isConnected, dynamic action) {
  if (action is SetNetworkStatusAction) {
    return action.isConnected;
  }
  
  return isConnected;
}
