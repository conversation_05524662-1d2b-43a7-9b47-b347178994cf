// Redux Store配置 - 全局状态管理中心
// 遵循熨烫主题命名和差异化规则

import 'package:redux/redux.dart';
import 'package:redux_thunk/redux_thunk.dart';

import '../FabricVault/irx_ai_expert_entity.dart';
import 'irx_app_state.dart';
import 'irx_heat_reducer.dart';
import 'irx_press_actions.dart';

class IrxStoreConfig {
  static Store<IrxAppState>? _store;

  // 获取Store实例（单例模式）
  static Store<IrxAppState> get store {
    _store ??= _createStore();
    return _store!;
  }

  // 创建Store
  static Store<IrxAppState> _createStore() {
    // 中间件配置
    final List<Middleware<IrxAppState>> middleware = [
      thunkMiddleware, // 支持异步Action
      _loggingMiddleware, // 日志中间件
    ];

    // 创建Store
    final store = Store<IrxAppState>(
      irxAppReducer,
      initialState: IrxAppState.initial(),
      middleware: middleware,
    );

    // 初始化应用数据
    _initializeApp(store);

    return store;
  }

  // 日志中间件 - 用于调试
  static Middleware<IrxAppState> _loggingMiddleware = (Store<IrxAppState> store, dynamic action, NextDispatcher next) {
    print('🔥 Redux Action: ${action.runtimeType}');
    print('📊 Current State: AI Experts: ${store.state.aiExperts.length}, '
          'Chat Records: ${store.state.chatRecords.length}, '
          'Tasks: ${store.state.taskItems.length}');
    
    next(action);
    
    print('✅ Action Processed: ${action.runtimeType}');
    print('---');
  };

  // 初始化应用数据
  static void _initializeApp(Store<IrxAppState> store) {
    // 初始化AI专家数据
    final experts = IrxAiExpertEntity.createDefaultExperts();
    store.dispatch(InitAiExpertsAction(experts));
    
    // 标记应用已初始化
    store.dispatch(SetAppInitializedAction(true));
    
    print('🚀 Irxz App Store Initialized');
    print('👥 AI Experts Loaded: ${experts.length}');
  }

  // 重置Store（用于测试或重新初始化）
  static void resetStore() {
    _store = null;
  }

  // 获取当前状态
  static IrxAppState get currentState => store.state;

  // 便捷方法：获取AI专家列表
  static List<IrxAiExpertEntity> get aiExperts => currentState.aiExperts;

  // 便捷方法：获取选中的专家
  static IrxAiExpertEntity? get selectedExpert => currentState.selectedExpert;

  // 便捷方法：检查是否有网络连接
  static bool get isNetworkConnected => currentState.isNetworkConnected;

  // 便捷方法：检查应用是否已初始化
  static bool get isAppInitialized => currentState.isInitialized;

  // 便捷方法：获取聊天记录数量
  static int get chatRecordsCount => currentState.chatRecords.length;

  // 便捷方法：获取任务数量
  static int get taskItemsCount => currentState.taskItems.length;

  // 便捷方法：获取已完成任务数量
  static int get completedTasksCount => 
      currentState.taskItems.where((task) => task.isCompleted).length;

  // 便捷方法：获取用户信息
  static String get userNickname => 
      currentState.userProfile?.nickname ?? 'Ironing Expert';

  // 便捷方法：检查专家是否还有免费使用次数
  static bool hasFreeTries(String expertId) {
    final expert = aiExperts.firstWhere(
      (e) => e.id == expertId,
      orElse: () => throw Exception('Expert not found: $expertId'),
    );
    return expert.hasFreeTries;
  }

  // 便捷方法：获取专家剩余免费次数
  static int getRemainingFreeTries(String expertId) {
    final expert = aiExperts.firstWhere(
      (e) => e.id == expertId,
      orElse: () => throw Exception('Expert not found: $expertId'),
    );
    return expert.remainingFreeTries;
  }

  // 便捷方法：按分类获取专家
  static List<IrxAiExpertEntity> getExpertsByCategory(String category) {
    return aiExperts.where((expert) => expert.category == category).toList();
  }

  // 便捷方法：获取所有分类
  static List<String> get allCategories {
    final categories = aiExperts.map((expert) => expert.category).toSet().toList();
    categories.sort();
    return categories;
  }

  // 便捷方法：获取特定专家的聊天记录
  static List<dynamic> getChatRecordsForExpert(String expertId) {
    return currentState.chatRecords
        .where((record) => record.expertId == expertId)
        .toList();
  }

  // 便捷方法：获取待处理任务
  static List<dynamic> get pendingTasks {
    return currentState.taskItems
        .where((task) => task.isPending)
        .toList();
  }

  // 便捷方法：获取今日任务
  static List<dynamic> get todayTasks {
    final today = DateTime.now();
    return currentState.taskItems.where((task) {
      return task.createdAt.year == today.year &&
             task.createdAt.month == today.month &&
             task.createdAt.day == today.day;
    }).toList();
  }

  // 便捷方法：检查是否有错误状态
  static bool get hasErrors {
    return currentState.globalError != null || currentState.chatError != null;
  }

  // 便捷方法：获取错误信息
  static String? get errorMessage {
    return currentState.globalError ?? currentState.chatError;
  }

  // 便捷方法：检查是否正在加载
  static bool get isLoading {
    return currentState.isChatLoading || 
           currentState.isTaskLoading || 
           currentState.isUserLoading;
  }
}
