// Redux Actions - 操作动作定义
// 使用熨烫主题命名，遵循差异化规则

import '../FabricVault/irx_ai_expert_entity.dart';
import '../FabricVault/irx_chat_record_entity.dart';
import '../FabricVault/irx_task_item_entity.dart';
import '../FabricVault/irx_user_profile_entity.dart';

// ============ AI专家相关Actions ============

/// 初始化AI专家列表
class InitAiExpertsAction {
  final List<IrxAiExpertEntity> experts;
  
  InitAiExpertsAction(this.experts);
  
  @override
  String toString() => 'InitAiExpertsAction{experts: ${experts.length}}';
}

/// 选择AI专家
class SelectAiExpertAction {
  final IrxAiExpertEntity expert;
  
  SelectAiExpertAction(this.expert);
  
  @override
  String toString() => 'SelectAiExpertAction{expert: ${expert.codeName}}';
}

/// 更新专家使用次数
class UpdateExpertUsageAction {
  final String expertId;
  final int usedCount;
  
  UpdateExpertUsageAction(this.expertId, this.usedCount);
  
  @override
  String toString() => 'UpdateExpertUsageAction{expertId: $expertId, usedCount: $usedCount}';
}

// ============ 聊天对话相关Actions ============

/// 开始聊天加载
class StartChatLoadingAction {
  @override
  String toString() => 'StartChatLoadingAction{}';
}

/// 停止聊天加载
class StopChatLoadingAction {
  @override
  String toString() => 'StopChatLoadingAction{}';
}

/// 添加聊天记录
class AddChatRecordAction {
  final IrxChatRecordEntity record;
  
  AddChatRecordAction(this.record);
  
  @override
  String toString() => 'AddChatRecordAction{record: ${record.id}}';
}

/// 批量加载聊天历史
class LoadChatHistoryAction {
  final List<IrxChatRecordEntity> records;
  
  LoadChatHistoryAction(this.records);
  
  @override
  String toString() => 'LoadChatHistoryAction{records: ${records.length}}';
}

/// 删除聊天记录
class DeleteChatRecordAction {
  final String recordId;
  
  DeleteChatRecordAction(this.recordId);
  
  @override
  String toString() => 'DeleteChatRecordAction{recordId: $recordId}';
}

/// 清空聊天历史
class ClearChatHistoryAction {
  final String? expertId; // 如果为null则清空所有
  
  ClearChatHistoryAction({this.expertId});
  
  @override
  String toString() => 'ClearChatHistoryAction{expertId: $expertId}';
}

/// 设置聊天错误
class SetChatErrorAction {
  final String? error;
  
  SetChatErrorAction(this.error);
  
  @override
  String toString() => 'SetChatErrorAction{error: $error}';
}

// ============ 待办任务相关Actions ============

/// 开始任务加载
class StartTaskLoadingAction {
  @override
  String toString() => 'StartTaskLoadingAction{}';
}

/// 停止任务加载
class StopTaskLoadingAction {
  @override
  String toString() => 'StopTaskLoadingAction{}';
}

/// 添加任务
class AddTaskItemAction {
  final IrxTaskItemEntity task;
  
  AddTaskItemAction(this.task);
  
  @override
  String toString() => 'AddTaskItemAction{task: ${task.id}}';
}

/// 更新任务
class UpdateTaskItemAction {
  final IrxTaskItemEntity task;
  
  UpdateTaskItemAction(this.task);
  
  @override
  String toString() => 'UpdateTaskItemAction{task: ${task.id}}';
}

/// 删除任务
class DeleteTaskItemAction {
  final String taskId;
  
  DeleteTaskItemAction(this.taskId);
  
  @override
  String toString() => 'DeleteTaskItemAction{taskId: $taskId}';
}

/// 切换任务完成状态
class ToggleTaskCompletionAction {
  final String taskId;
  
  ToggleTaskCompletionAction(this.taskId);
  
  @override
  String toString() => 'ToggleTaskCompletionAction{taskId: $taskId}';
}

/// 加载任务列表
class LoadTaskListAction {
  final List<IrxTaskItemEntity> tasks;
  
  LoadTaskListAction(this.tasks);
  
  @override
  String toString() => 'LoadTaskListAction{tasks: ${tasks.length}}';
}

// ============ 用户信息相关Actions ============

/// 开始用户加载
class StartUserLoadingAction {
  @override
  String toString() => 'StartUserLoadingAction{}';
}

/// 停止用户加载
class StopUserLoadingAction {
  @override
  String toString() => 'StopUserLoadingAction{}';
}

/// 设置用户信息
class SetUserProfileAction {
  final IrxUserProfileEntity profile;
  
  SetUserProfileAction(this.profile);
  
  @override
  String toString() => 'SetUserProfileAction{profile: ${profile.id}}';
}

/// 更新用户头像
class UpdateUserAvatarAction {
  final String avatarPath;
  
  UpdateUserAvatarAction(this.avatarPath);
  
  @override
  String toString() => 'UpdateUserAvatarAction{avatarPath: $avatarPath}';
}

/// 更新用户昵称
class UpdateUserNicknameAction {
  final String nickname;
  
  UpdateUserNicknameAction(this.nickname);
  
  @override
  String toString() => 'UpdateUserNicknameAction{nickname: $nickname}';
}

// ============ 应用通用Actions ============

/// 设置应用初始化状态
class SetAppInitializedAction {
  final bool isInitialized;
  
  SetAppInitializedAction(this.isInitialized);
  
  @override
  String toString() => 'SetAppInitializedAction{isInitialized: $isInitialized}';
}

/// 设置全局错误
class SetGlobalErrorAction {
  final String? error;
  
  SetGlobalErrorAction(this.error);
  
  @override
  String toString() => 'SetGlobalErrorAction{error: $error}';
}

/// 设置网络连接状态
class SetNetworkStatusAction {
  final bool isConnected;
  
  SetNetworkStatusAction(this.isConnected);
  
  @override
  String toString() => 'SetNetworkStatusAction{isConnected: $isConnected}';
}
