// Redux应用状态管理 - 全局状态定义
// 遵循命名差异化规则，使用熨烫主题命名

import 'package:meta/meta.dart';
import '../FabricVault/irx_ai_expert_entity.dart';
import '../FabricVault/irx_chat_record_entity.dart';
import '../FabricVault/irx_task_item_entity.dart';
import '../FabricVault/irx_user_profile_entity.dart';

@immutable
class IrxAppState {
  // AI专家角色状态
  final List<IrxAiExpertEntity> aiExperts;
  final IrxAiExpertEntity? selectedExpert;
  
  // 聊天对话状态
  final List<IrxChatRecordEntity> chatRecords;
  final bool isChatLoading;
  final String? chatError;
  
  // 待办任务状态
  final List<IrxTaskItemEntity> taskItems;
  final bool isTaskLoading;
  
  // 用户信息状态
  final IrxUserProfileEntity? userProfile;
  final bool isUserLoading;
  
  // 应用通用状态
  final bool isInitialized;
  final String? globalError;
  final bool isNetworkConnected;

  const IrxAppState({
    this.aiExperts = const [],
    this.selectedExpert,
    this.chatRecords = const [],
    this.isChatLoading = false,
    this.chatError,
    this.taskItems = const [],
    this.isTaskLoading = false,
    this.userProfile,
    this.isUserLoading = false,
    this.isInitialized = false,
    this.globalError,
    this.isNetworkConnected = true,
  });

  // 创建初始状态
  factory IrxAppState.initial() {
    return const IrxAppState(
      aiExperts: [],
      selectedExpert: null,
      chatRecords: [],
      isChatLoading: false,
      chatError: null,
      taskItems: [],
      isTaskLoading: false,
      userProfile: null,
      isUserLoading: false,
      isInitialized: false,
      globalError: null,
      isNetworkConnected: true,
    );
  }

  // 状态复制方法
  IrxAppState copyWith({
    List<IrxAiExpertEntity>? aiExperts,
    IrxAiExpertEntity? selectedExpert,
    List<IrxChatRecordEntity>? chatRecords,
    bool? isChatLoading,
    String? chatError,
    List<IrxTaskItemEntity>? taskItems,
    bool? isTaskLoading,
    IrxUserProfileEntity? userProfile,
    bool? isUserLoading,
    bool? isInitialized,
    String? globalError,
    bool? isNetworkConnected,
  }) {
    return IrxAppState(
      aiExperts: aiExperts ?? this.aiExperts,
      selectedExpert: selectedExpert ?? this.selectedExpert,
      chatRecords: chatRecords ?? this.chatRecords,
      isChatLoading: isChatLoading ?? this.isChatLoading,
      chatError: chatError ?? this.chatError,
      taskItems: taskItems ?? this.taskItems,
      isTaskLoading: isTaskLoading ?? this.isTaskLoading,
      userProfile: userProfile ?? this.userProfile,
      isUserLoading: isUserLoading ?? this.isUserLoading,
      isInitialized: isInitialized ?? this.isInitialized,
      globalError: globalError ?? this.globalError,
      isNetworkConnected: isNetworkConnected ?? this.isNetworkConnected,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is IrxAppState &&
          runtimeType == other.runtimeType &&
          aiExperts == other.aiExperts &&
          selectedExpert == other.selectedExpert &&
          chatRecords == other.chatRecords &&
          isChatLoading == other.isChatLoading &&
          chatError == other.chatError &&
          taskItems == other.taskItems &&
          isTaskLoading == other.isTaskLoading &&
          userProfile == other.userProfile &&
          isUserLoading == other.isUserLoading &&
          isInitialized == other.isInitialized &&
          globalError == other.globalError &&
          isNetworkConnected == other.isNetworkConnected;

  @override
  int get hashCode =>
      aiExperts.hashCode ^
      selectedExpert.hashCode ^
      chatRecords.hashCode ^
      isChatLoading.hashCode ^
      chatError.hashCode ^
      taskItems.hashCode ^
      isTaskLoading.hashCode ^
      userProfile.hashCode ^
      isUserLoading.hashCode ^
      isInitialized.hashCode ^
      globalError.hashCode ^
      isNetworkConnected.hashCode;

  @override
  String toString() {
    return 'IrxAppState{'
        'aiExperts: ${aiExperts.length}, '
        'selectedExpert: $selectedExpert, '
        'chatRecords: ${chatRecords.length}, '
        'isChatLoading: $isChatLoading, '
        'chatError: $chatError, '
        'taskItems: ${taskItems.length}, '
        'isTaskLoading: $isTaskLoading, '
        'userProfile: $userProfile, '
        'isUserLoading: $isUserLoading, '
        'isInitialized: $isInitialized, '
        'globalError: $globalError, '
        'isNetworkConnected: $isNetworkConnected'
        '}';
  }
}
