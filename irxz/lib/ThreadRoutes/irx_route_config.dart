// 路由配置 - 应用导航管理
// 遵循熨烫主题命名和差异化规则

import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';

import '../LoomAtelier/irx_main_hub_view.dart';
import '../FabricVault/irx_ai_expert_entity.dart';

class IrxRouteConfig {
  // 路由名称常量
  static const String mainHub = '/';
  static const String chatFlow = '/chat';
  static const String taskMgr = '/tasks';
  static const String chatHistory = '/history';
  static const String userCenter = '/profile';
  static const String settings = '/settings';

  // 路由生成器
  static Route<dynamic> generateRoute(RouteSettings settings) {
    switch (settings.name) {
      case mainHub:
        return _createRoute(const IrxMainHubView());
      
      case chatFlow:
        final expert = settings.arguments as IrxAiExpertEntity?;
        if (expert != null) {
          return _createRoute(
            // TODO: 创建聊天页面
            _PlaceholderPage(
              title: 'Chat with ${expert.displayName}',
              subtitle: 'Coming Soon...',
              icon: CupertinoIcons.chat_bubble_2_fill,
            ),
          );
        }
        return _createErrorRoute('Expert not found');
      
      case taskMgr:
        return _createRoute(
          // TODO: 创建任务管理页面
          const _PlaceholderPage(
            title: 'Quick Tasks',
            subtitle: 'Mini To-Do List',
            icon: CupertinoIcons.list_bullet,
          ),
        );
      
      case chatHistory:
        return _createRoute(
          // TODO: 创建聊天历史页面
          const _PlaceholderPage(
            title: 'Chat History',
            subtitle: 'Your Conversations',
            icon: CupertinoIcons.clock_fill,
          ),
        );
      
      case userCenter:
        return _createRoute(
          // TODO: 创建用户中心页面
          const _PlaceholderPage(
            title: 'User Center',
            subtitle: 'Profile & Settings',
            icon: CupertinoIcons.person_crop_circle_fill,
          ),
        );
      
      case settings:
        return _createRoute(
          // TODO: 创建设置页面
          const _PlaceholderPage(
            title: 'Settings',
            subtitle: 'App Preferences',
            icon: CupertinoIcons.gear_alt_fill,
          ),
        );
      
      default:
        return _createErrorRoute('Route not found: ${settings.name}');
    }
  }

  // 创建页面路由（iOS风格过渡动画）
  static PageRouteBuilder _createRoute(Widget page) {
    return PageRouteBuilder(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: const Duration(milliseconds: 300),
      reverseTransitionDuration: const Duration(milliseconds: 300),
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        // iOS风格滑动过渡
        const begin = Offset(1.0, 0.0);
        const end = Offset.zero;
        const curve = Curves.easeInOut;

        var tween = Tween(begin: begin, end: end).chain(
          CurveTween(curve: curve),
        );

        return SlideTransition(
          position: animation.drive(tween),
          child: child,
        );
      },
    );
  }

  // 创建错误路由
  static Route<dynamic> _createErrorRoute(String message) {
    return MaterialPageRoute(
      builder: (context) => Scaffold(
        appBar: AppBar(
          title: const Text('Error'),
          backgroundColor: const Color(0xFFE74C3C),
          foregroundColor: Colors.white,
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                CupertinoIcons.exclamationmark_triangle_fill,
                size: 64,
                color: Color(0xFFE74C3C),
              ),
              const SizedBox(height: 16),
              Text(
                'Oops!',
                style: const TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF2C3E50),
                ),
              ),
              const SizedBox(height: 8),
              Text(
                message,
                style: const TextStyle(
                  fontSize: 16,
                  color: Color(0xFF7F8C8D),
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              ElevatedButton.icon(
                onPressed: () => Navigator.of(context).pop(),
                icon: const Icon(CupertinoIcons.back),
                label: const Text('Go Back'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF3498DB),
                  foregroundColor: Colors.white,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// 临时占位页面（用于未完成的页面）
class _PlaceholderPage extends StatelessWidget {
  final String title;
  final String subtitle;
  final IconData icon;

  const _PlaceholderPage({
    required this.title,
    required this.subtitle,
    required this.icon,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      appBar: AppBar(
        title: Text(title),
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(CupertinoIcons.back),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [
                    Color(0xFF3498DB),
                    Color(0xFF1ABC9C),
                  ],
                ),
                borderRadius: BorderRadius.circular(60),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0xFF3498DB).withOpacity(0.3),
                    blurRadius: 20,
                    offset: const Offset(0, 8),
                  ),
                ],
              ),
              child: Icon(
                icon,
                size: 60,
                color: Colors.white,
              ),
            ),
            
            const SizedBox(height: 32),
            
            Text(
              title,
              style: const TextStyle(
                fontSize: 28,
                fontWeight: FontWeight.bold,
                color: Color(0xFF2C3E50),
              ),
            ),
            
            const SizedBox(height: 8),
            
            Text(
              subtitle,
              style: const TextStyle(
                fontSize: 16,
                color: Color(0xFF7F8C8D),
              ),
            ),
            
            const SizedBox(height: 24),
            
            Container(
              padding: const EdgeInsets.symmetric(
                horizontal: 20,
                vertical: 12,
              ),
              decoration: BoxDecoration(
                color: const Color(0xFFF1C40F).withOpacity(0.1),
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                  color: const Color(0xFFF1C40F).withOpacity(0.3),
                ),
              ),
              child: const Text(
                '🚧 Under Development',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFFF39C12),
                ),
              ),
            ),
            
            const SizedBox(height: 40),
            
            ElevatedButton.icon(
              onPressed: () => Navigator.of(context).pop(),
              icon: const Icon(CupertinoIcons.back),
              label: const Text('Back to Home'),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF3498DB),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
