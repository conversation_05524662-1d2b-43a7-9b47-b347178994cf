// AI专家实体类 - 数据模型定义
// 遵循熨烫主题命名和差异化规则

import 'package:meta/meta.dart';

@immutable
class IrxAiExpertEntity {
  final String id;
  final String codeName; // 英文代号，如CottonIron
  final String displayName; // 显示名称，如Cotton Ironing Expert
  final String shortDesc; // 短介绍
  final String longDesc; // 详细介绍
  final String avatarPath; // 头像路径
  final String category; // 分类标签
  final int freeUsageLimit; // 免费使用次数限制
  final int usedCount; // 已使用次数
  final bool isActive; // 是否激活
  final DateTime createdAt;
  final DateTime updatedAt;

  const IrxAiExpertEntity({
    required this.id,
    required this.codeName,
    required this.displayName,
    required this.shortDesc,
    required this.longDesc,
    required this.avatarPath,
    required this.category,
    this.freeUsageLimit = 3,
    this.usedCount = 0,
    this.isActive = true,
    required this.createdAt,
    required this.updatedAt,
  });

  // 工厂方法：创建默认的10个AI专家
  static List<IrxAiExpertEntity> createDefaultExperts() {
    final now = DateTime.now();
    
    return [
      IrxAiExpertEntity(
        id: 'expert_001',
        codeName: 'CottonIron',
        displayName: 'Cotton Ironing Expert',
        shortDesc: 'Teach simple ironing methods for cotton clothes.',
        longDesc: 'Set iron to medium-high heat (150-180℃), use steam. Iron while slightly damp, work from collar to hem. For thick cotton, press with more pressure; for thin, lighter strokes to avoid shine.',
        avatarPath: 'assets/Irx/irx_1.png',
        category: 'Fabric Care',
        createdAt: now,
        updatedAt: now,
      ),
      IrxAiExpertEntity(
        id: 'expert_002',
        codeName: 'WoolIron',
        displayName: 'Wool Ironing Expert',
        shortDesc: 'Guide easy ironing for wool garments.',
        longDesc: 'Use low heat (100-120℃) with a cloth barrier. Steam gently, avoid pressing too hard to prevent stretching. Iron inside out, focus on seams first, then flat areas.',
        avatarPath: 'assets/Irx/irx_2.png',
        category: 'Fabric Care',
        createdAt: now,
        updatedAt: now,
      ),
      IrxAiExpertEntity(
        id: 'expert_003',
        codeName: 'SilkIron',
        displayName: 'Silk Ironing Expert',
        shortDesc: 'Instruct safe ironing for delicate silk items.',
        longDesc: 'Set to low heat (80-100℃), always use a pressing cloth. Iron on reverse side, avoid steam directly on fabric. Glide lightly, stop if fabric feels hot to touch.',
        avatarPath: 'assets/Irx/irx_3.png',
        category: 'Fabric Care',
        createdAt: now,
        updatedAt: now,
      ),
      IrxAiExpertEntity(
        id: 'expert_004',
        codeName: 'SyntheticIron',
        displayName: 'Synthetic Ironing Expert',
        shortDesc: 'Teach simple ironing for polyester, nylon, etc.',
        longDesc: 'Use low heat (110-130℃), no steam for some synthetics. Iron quickly to prevent melting, keep iron moving. For blends, follow the lower heat of the fabric types.',
        avatarPath: 'assets/Irx/irx_4.png',
        category: 'Fabric Care',
        createdAt: now,
        updatedAt: now,
      ),
      IrxAiExpertEntity(
        id: 'expert_005',
        codeName: 'ShirtQuickIron',
        displayName: 'Shirt Quick Ironing Expert',
        shortDesc: 'Provide time-saving ironing steps for shirts.',
        longDesc: 'Start with collar (inside then outside), cuffs, yoke, sleeves, then body. Use steam for stubborn wrinkles, fold immediately after to keep neat.',
        avatarPath: 'assets/Irx/irx_5.png',
        category: 'Quick Tips',
        createdAt: now,
        updatedAt: now,
      ),
      IrxAiExpertEntity(
        id: 'expert_006',
        codeName: 'PantsIron',
        displayName: 'Pants Ironing Expert',
        shortDesc: 'Guide easy ironing for trousers and jeans.',
        longDesc: 'Iron along creases for formal pants; skip creases for jeans. Use medium heat, iron inside out for dark colors to avoid fading. Steam pockets flat before starting.',
        avatarPath: 'assets/Irx/irx_6.png',
        category: 'Garment Care',
        createdAt: now,
        updatedAt: now,
      ),
      IrxAiExpertEntity(
        id: 'expert_007',
        codeName: 'SteamerIron',
        displayName: 'Steamer Ironing Expert',
        shortDesc: 'Teach simple use of garment steamers for clothes.',
        longDesc: 'Hold steamer 10-15cm from fabric, move downward. Start with collars/sleeves, then main body. For thick items, steam longer; for delicate, keep distance.',
        avatarPath: 'assets/Irx/irx_7.png',
        category: 'Tools',
        createdAt: now,
        updatedAt: now,
      ),
      IrxAiExpertEntity(
        id: 'expert_008',
        codeName: 'WrinkleFix',
        displayName: 'Wrinkle Fix Expert',
        shortDesc: 'Offer quick fixes for lightly wrinkled clothes.',
        longDesc: 'Use a damp towel and microwave (30s) for small items, or hang in a steamy bathroom. For travel, roll clothes with tissue paper to minimize wrinkles.',
        avatarPath: 'assets/Irx/irx_8.png',
        category: 'Quick Tips',
        createdAt: now,
        updatedAt: now,
      ),
      IrxAiExpertEntity(
        id: 'expert_009',
        codeName: 'IronTool',
        displayName: 'Iron Tool Expert',
        shortDesc: 'Guide proper use of ironing boards and accessories.',
        longDesc: 'Adjust board height for comfort, use a clean cover. Attach clips for sleeves, use a point presser for collars. Keep iron base clean to avoid staining.',
        avatarPath: 'assets/Irx/irx_9.png',
        category: 'Tools',
        createdAt: now,
        updatedAt: now,
      ),
      IrxAiExpertEntity(
        id: 'expert_010',
        codeName: 'NoIronTips',
        displayName: 'No-Iron Tips Expert',
        shortDesc: 'Provide tips to reduce or avoid ironing needs.',
        longDesc: 'Suggest hanging clothes immediately after washing, using fabric softener, or line-drying properly. Fold knitwear flat to prevent creases.',
        avatarPath: 'assets/Irx/irx_10.png',
        category: 'Prevention',
        createdAt: now,
        updatedAt: now,
      ),
    ];
  }

  // 检查是否还有免费使用次数
  bool get hasFreeTries => usedCount < freeUsageLimit;
  
  // 获取剩余免费次数
  int get remainingFreeTries => (freeUsageLimit - usedCount).clamp(0, freeUsageLimit);

  // 复制方法
  IrxAiExpertEntity copyWith({
    String? id,
    String? codeName,
    String? displayName,
    String? shortDesc,
    String? longDesc,
    String? avatarPath,
    String? category,
    int? freeUsageLimit,
    int? usedCount,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return IrxAiExpertEntity(
      id: id ?? this.id,
      codeName: codeName ?? this.codeName,
      displayName: displayName ?? this.displayName,
      shortDesc: shortDesc ?? this.shortDesc,
      longDesc: longDesc ?? this.longDesc,
      avatarPath: avatarPath ?? this.avatarPath,
      category: category ?? this.category,
      freeUsageLimit: freeUsageLimit ?? this.freeUsageLimit,
      usedCount: usedCount ?? this.usedCount,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // JSON序列化
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'codeName': codeName,
      'displayName': displayName,
      'shortDesc': shortDesc,
      'longDesc': longDesc,
      'avatarPath': avatarPath,
      'category': category,
      'freeUsageLimit': freeUsageLimit,
      'usedCount': usedCount,
      'isActive': isActive,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  // JSON反序列化
  factory IrxAiExpertEntity.fromJson(Map<String, dynamic> json) {
    return IrxAiExpertEntity(
      id: json['id'] as String,
      codeName: json['codeName'] as String,
      displayName: json['displayName'] as String,
      shortDesc: json['shortDesc'] as String,
      longDesc: json['longDesc'] as String,
      avatarPath: json['avatarPath'] as String,
      category: json['category'] as String,
      freeUsageLimit: json['freeUsageLimit'] as int? ?? 3,
      usedCount: json['usedCount'] as int? ?? 0,
      isActive: json['isActive'] as bool? ?? true,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is IrxAiExpertEntity &&
          runtimeType == other.runtimeType &&
          id == other.id &&
          codeName == other.codeName &&
          usedCount == other.usedCount;

  @override
  int get hashCode => id.hashCode ^ codeName.hashCode ^ usedCount.hashCode;

  @override
  String toString() {
    return 'IrxAiExpertEntity{id: $id, codeName: $codeName, displayName: $displayName, usedCount: $usedCount/$freeUsageLimit}';
  }
}
