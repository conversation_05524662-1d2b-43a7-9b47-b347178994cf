// 用户信息实体类 - 用户数据模型
// 遵循熨烫主题命名和差异化规则

import 'package:meta/meta.dart';

@immutable
class IrxUserProfileEntity {
  final String id;
  final String nickname; // 用户昵称
  final String? email; // 邮箱（可选）
  final String avatarPath; // 头像路径
  final List<String> favoriteExpertIds; // 收藏的专家ID列表
  final Map<String, int> expertUsageCount; // 各专家使用次数统计
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? lastActiveAt; // 最后活跃时间
  final Map<String, dynamic>? preferences; // 用户偏好设置
  final Map<String, dynamic>? metadata; // 额外数据

  const IrxUserProfileEntity({
    required this.id,
    required this.nickname,
    this.email,
    required this.avatarPath,
    this.favoriteExpertIds = const [],
    this.expertUsageCount = const {},
    required this.createdAt,
    required this.updatedAt,
    this.lastActiveAt,
    this.preferences,
    this.metadata,
  });

  // 工厂方法：创建默认用户
  factory IrxUserProfileEntity.createDefault({
    required String id,
    String nickname = 'Ironing Expert',
    String avatarPath = 'assets/Irx/avatars/avatar_portrait_1_3TLl_97HNJo.jpg',
  }) {
    final now = DateTime.now();
    return IrxUserProfileEntity(
      id: id,
      nickname: nickname,
      avatarPath: avatarPath,
      createdAt: now,
      updatedAt: now,
      lastActiveAt: now,
      preferences: {
        'theme': 'light',
        'language': 'en',
        'notifications': true,
        'autoSave': true,
      },
    );
  }

  // 检查是否收藏了某个专家
  bool isFavoriteExpert(String expertId) {
    return favoriteExpertIds.contains(expertId);
  }

  // 获取专家使用次数
  int getExpertUsageCount(String expertId) {
    return expertUsageCount[expertId] ?? 0;
  }

  // 获取总使用次数
  int get totalUsageCount {
    return expertUsageCount.values.fold(0, (sum, count) => sum + count);
  }

  // 获取最常用的专家ID
  String? get mostUsedExpertId {
    if (expertUsageCount.isEmpty) return null;
    
    String? mostUsedId;
    int maxCount = 0;
    
    expertUsageCount.forEach((expertId, count) {
      if (count > maxCount) {
        maxCount = count;
        mostUsedId = expertId;
      }
    });
    
    return mostUsedId;
  }

  // 添加收藏专家
  IrxUserProfileEntity addFavoriteExpert(String expertId) {
    if (favoriteExpertIds.contains(expertId)) return this;
    
    final newFavorites = List<String>.from(favoriteExpertIds)..add(expertId);
    return copyWith(
      favoriteExpertIds: newFavorites,
      updatedAt: DateTime.now(),
    );
  }

  // 移除收藏专家
  IrxUserProfileEntity removeFavoriteExpert(String expertId) {
    if (!favoriteExpertIds.contains(expertId)) return this;
    
    final newFavorites = List<String>.from(favoriteExpertIds)..remove(expertId);
    return copyWith(
      favoriteExpertIds: newFavorites,
      updatedAt: DateTime.now(),
    );
  }

  // 增加专家使用次数
  IrxUserProfileEntity incrementExpertUsage(String expertId) {
    final newUsageCount = Map<String, int>.from(expertUsageCount);
    newUsageCount[expertId] = (newUsageCount[expertId] ?? 0) + 1;
    
    return copyWith(
      expertUsageCount: newUsageCount,
      lastActiveAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  // 更新用户偏好
  IrxUserProfileEntity updatePreference(String key, dynamic value) {
    final newPreferences = Map<String, dynamic>.from(preferences ?? {});
    newPreferences[key] = value;
    
    return copyWith(
      preferences: newPreferences,
      updatedAt: DateTime.now(),
    );
  }

  // 更新最后活跃时间
  IrxUserProfileEntity updateLastActive() {
    return copyWith(lastActiveAt: DateTime.now());
  }

  // 复制方法
  IrxUserProfileEntity copyWith({
    String? id,
    String? nickname,
    String? email,
    String? avatarPath,
    List<String>? favoriteExpertIds,
    Map<String, int>? expertUsageCount,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? lastActiveAt,
    Map<String, dynamic>? preferences,
    Map<String, dynamic>? metadata,
  }) {
    return IrxUserProfileEntity(
      id: id ?? this.id,
      nickname: nickname ?? this.nickname,
      email: email ?? this.email,
      avatarPath: avatarPath ?? this.avatarPath,
      favoriteExpertIds: favoriteExpertIds ?? this.favoriteExpertIds,
      expertUsageCount: expertUsageCount ?? this.expertUsageCount,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      lastActiveAt: lastActiveAt ?? this.lastActiveAt,
      preferences: preferences ?? this.preferences,
      metadata: metadata ?? this.metadata,
    );
  }

  // JSON序列化
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'nickname': nickname,
      'email': email,
      'avatarPath': avatarPath,
      'favoriteExpertIds': favoriteExpertIds,
      'expertUsageCount': expertUsageCount,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'lastActiveAt': lastActiveAt?.toIso8601String(),
      'preferences': preferences,
      'metadata': metadata,
    };
  }

  // JSON反序列化
  factory IrxUserProfileEntity.fromJson(Map<String, dynamic> json) {
    return IrxUserProfileEntity(
      id: json['id'] as String,
      nickname: json['nickname'] as String,
      email: json['email'] as String?,
      avatarPath: json['avatarPath'] as String,
      favoriteExpertIds: List<String>.from(json['favoriteExpertIds'] as List? ?? []),
      expertUsageCount: Map<String, int>.from(json['expertUsageCount'] as Map? ?? {}),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      lastActiveAt: json['lastActiveAt'] != null 
          ? DateTime.parse(json['lastActiveAt'] as String) 
          : null,
      preferences: json['preferences'] as Map<String, dynamic>?,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  // 数据库映射
  Map<String, dynamic> toDbMap() {
    return {
      'id': id,
      'nickname': nickname,
      'email': email,
      'avatar_path': avatarPath,
      'favorite_expert_ids': favoriteExpertIds.join(','),
      'expert_usage_count': expertUsageCount.toString(), // 简单序列化
      'created_at': createdAt.millisecondsSinceEpoch,
      'updated_at': updatedAt.millisecondsSinceEpoch,
      'last_active_at': lastActiveAt?.millisecondsSinceEpoch,
      'preferences': preferences?.toString(),
      'metadata': metadata?.toString(),
    };
  }

  // 从数据库映射创建
  factory IrxUserProfileEntity.fromDbMap(Map<String, dynamic> map) {
    return IrxUserProfileEntity(
      id: map['id'] as String,
      nickname: map['nickname'] as String,
      email: map['email'] as String?,
      avatarPath: map['avatar_path'] as String,
      favoriteExpertIds: (map['favorite_expert_ids'] as String?)?.split(',') ?? [],
      expertUsageCount: {}, // 简化处理
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['created_at'] as int),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(map['updated_at'] as int),
      lastActiveAt: map['last_active_at'] != null 
          ? DateTime.fromMillisecondsSinceEpoch(map['last_active_at'] as int)
          : null,
      preferences: null, // 简化处理
      metadata: null, // 简化处理
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is IrxUserProfileEntity &&
          runtimeType == other.runtimeType &&
          id == other.id &&
          nickname == other.nickname &&
          avatarPath == other.avatarPath;

  @override
  int get hashCode => id.hashCode ^ nickname.hashCode ^ avatarPath.hashCode;

  @override
  String toString() {
    return 'IrxUserProfileEntity{id: $id, nickname: $nickname, totalUsage: $totalUsageCount, favorites: ${favoriteExpertIds.length}}';
  }
}
