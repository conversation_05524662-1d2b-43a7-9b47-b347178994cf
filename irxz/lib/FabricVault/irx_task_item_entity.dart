// 任务项实体类 - 待办清单数据模型
// 遵循熨烫主题命名和差异化规则

import 'package:meta/meta.dart';

enum IrxTaskPriority {
  low,     // 低优先级
  medium,  // 中优先级
  high,    // 高优先级
  urgent,  // 紧急
}

enum IrxTaskStatus {
  pending,    // 待处理
  inProgress, // 进行中
  completed,  // 已完成
  cancelled,  // 已取消
}

@immutable
class IrxTaskItemEntity {
  final String id;
  final String title; // 任务标题
  final String? description; // 任务描述
  final IrxTaskPriority priority;
  final IrxTaskStatus status;
  final DateTime createdAt;
  final DateTime? completedAt;
  final DateTime? dueDate; // 截止日期
  final List<String> tags; // 标签列表
  final Map<String, dynamic>? metadata; // 额外数据

  const IrxTaskItemEntity({
    required this.id,
    required this.title,
    this.description,
    this.priority = IrxTaskPriority.medium,
    this.status = IrxTaskStatus.pending,
    required this.createdAt,
    this.completedAt,
    this.dueDate,
    this.tags = const [],
    this.metadata,
  });

  // 工厂方法：创建新任务
  factory IrxTaskItemEntity.create({
    required String id,
    required String title,
    String? description,
    IrxTaskPriority priority = IrxTaskPriority.medium,
    DateTime? dueDate,
    List<String> tags = const [],
    Map<String, dynamic>? metadata,
  }) {
    return IrxTaskItemEntity(
      id: id,
      title: title,
      description: description,
      priority: priority,
      status: IrxTaskStatus.pending,
      createdAt: DateTime.now(),
      dueDate: dueDate,
      tags: tags,
      metadata: metadata,
    );
  }

  // 检查任务是否已完成
  bool get isCompleted => status == IrxTaskStatus.completed;
  
  // 检查任务是否进行中
  bool get isInProgress => status == IrxTaskStatus.inProgress;
  
  // 检查任务是否待处理
  bool get isPending => status == IrxTaskStatus.pending;
  
  // 检查任务是否已取消
  bool get isCancelled => status == IrxTaskStatus.cancelled;

  // 检查任务是否过期
  bool get isOverdue {
    if (dueDate == null || isCompleted) return false;
    return DateTime.now().isAfter(dueDate!);
  }

  // 获取优先级颜色
  String get priorityColor {
    switch (priority) {
      case IrxTaskPriority.low:
        return '#95A5A6'; // 灰色
      case IrxTaskPriority.medium:
        return '#3498DB'; // 蒸汽蓝
      case IrxTaskPriority.high:
        return '#F1C40F'; // 金黄
      case IrxTaskPriority.urgent:
        return '#E74C3C'; // 红色
    }
  }

  // 获取优先级文本
  String get priorityText {
    switch (priority) {
      case IrxTaskPriority.low:
        return 'Low';
      case IrxTaskPriority.medium:
        return 'Medium';
      case IrxTaskPriority.high:
        return 'High';
      case IrxTaskPriority.urgent:
        return 'Urgent';
    }
  }

  // 获取状态文本
  String get statusText {
    switch (status) {
      case IrxTaskStatus.pending:
        return 'Pending';
      case IrxTaskStatus.inProgress:
        return 'In Progress';
      case IrxTaskStatus.completed:
        return 'Completed';
      case IrxTaskStatus.cancelled:
        return 'Cancelled';
    }
  }

  // 标记为完成
  IrxTaskItemEntity markAsCompleted() {
    return copyWith(
      status: IrxTaskStatus.completed,
      completedAt: DateTime.now(),
    );
  }

  // 标记为进行中
  IrxTaskItemEntity markAsInProgress() {
    return copyWith(status: IrxTaskStatus.inProgress);
  }

  // 标记为待处理
  IrxTaskItemEntity markAsPending() {
    return copyWith(
      status: IrxTaskStatus.pending,
      completedAt: null,
    );
  }

  // 标记为取消
  IrxTaskItemEntity markAsCancelled() {
    return copyWith(status: IrxTaskStatus.cancelled);
  }

  // 复制方法
  IrxTaskItemEntity copyWith({
    String? id,
    String? title,
    String? description,
    IrxTaskPriority? priority,
    IrxTaskStatus? status,
    DateTime? createdAt,
    DateTime? completedAt,
    DateTime? dueDate,
    List<String>? tags,
    Map<String, dynamic>? metadata,
  }) {
    return IrxTaskItemEntity(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      priority: priority ?? this.priority,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      completedAt: completedAt ?? this.completedAt,
      dueDate: dueDate ?? this.dueDate,
      tags: tags ?? this.tags,
      metadata: metadata ?? this.metadata,
    );
  }

  // JSON序列化
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'priority': priority.name,
      'status': status.name,
      'createdAt': createdAt.toIso8601String(),
      'completedAt': completedAt?.toIso8601String(),
      'dueDate': dueDate?.toIso8601String(),
      'tags': tags,
      'metadata': metadata,
    };
  }

  // JSON反序列化
  factory IrxTaskItemEntity.fromJson(Map<String, dynamic> json) {
    return IrxTaskItemEntity(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String?,
      priority: IrxTaskPriority.values.firstWhere(
        (e) => e.name == json['priority'],
        orElse: () => IrxTaskPriority.medium,
      ),
      status: IrxTaskStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => IrxTaskStatus.pending,
      ),
      createdAt: DateTime.parse(json['createdAt'] as String),
      completedAt: json['completedAt'] != null 
          ? DateTime.parse(json['completedAt'] as String) 
          : null,
      dueDate: json['dueDate'] != null 
          ? DateTime.parse(json['dueDate'] as String) 
          : null,
      tags: List<String>.from(json['tags'] as List? ?? []),
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  // 数据库映射
  Map<String, dynamic> toDbMap() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'priority': priority.name,
      'status': status.name,
      'created_at': createdAt.millisecondsSinceEpoch,
      'completed_at': completedAt?.millisecondsSinceEpoch,
      'due_date': dueDate?.millisecondsSinceEpoch,
      'tags': tags.join(','), // 简单序列化
      'metadata': metadata?.toString(),
    };
  }

  // 从数据库映射创建
  factory IrxTaskItemEntity.fromDbMap(Map<String, dynamic> map) {
    return IrxTaskItemEntity(
      id: map['id'] as String,
      title: map['title'] as String,
      description: map['description'] as String?,
      priority: IrxTaskPriority.values.firstWhere(
        (e) => e.name == map['priority'],
        orElse: () => IrxTaskPriority.medium,
      ),
      status: IrxTaskStatus.values.firstWhere(
        (e) => e.name == map['status'],
        orElse: () => IrxTaskStatus.pending,
      ),
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['created_at'] as int),
      completedAt: map['completed_at'] != null 
          ? DateTime.fromMillisecondsSinceEpoch(map['completed_at'] as int)
          : null,
      dueDate: map['due_date'] != null 
          ? DateTime.fromMillisecondsSinceEpoch(map['due_date'] as int)
          : null,
      tags: (map['tags'] as String?)?.split(',') ?? [],
      metadata: null, // 简化处理
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is IrxTaskItemEntity &&
          runtimeType == other.runtimeType &&
          id == other.id &&
          title == other.title &&
          status == other.status;

  @override
  int get hashCode => id.hashCode ^ title.hashCode ^ status.hashCode;

  @override
  String toString() {
    return 'IrxTaskItemEntity{id: $id, title: $title, status: $status, priority: $priority}';
  }
}
