// 聊天记录实体类 - 对话数据模型
// 遵循熨烫主题命名和差异化规则

import 'package:meta/meta.dart';

enum IrxMessageType {
  user,    // 用户消息
  ai,      // AI回复
  system,  // 系统消息
}

@immutable
class IrxChatRecordEntity {
  final String id;
  final String expertId; // 关联的AI专家ID
  final String expertCodeName; // AI专家代号
  final IrxMessageType messageType;
  final String content; // 消息内容
  final DateTime timestamp;
  final bool isRead; // 是否已读
  final Map<String, dynamic>? metadata; // 额外元数据

  const IrxChatRecordEntity({
    required this.id,
    required this.expertId,
    required this.expertCodeName,
    required this.messageType,
    required this.content,
    required this.timestamp,
    this.isRead = false,
    this.metadata,
  });

  // 工厂方法：创建用户消息
  factory IrxChatRecordEntity.userMessage({
    required String id,
    required String expertId,
    required String expertCodeName,
    required String content,
    DateTime? timestamp,
    Map<String, dynamic>? metadata,
  }) {
    return IrxChatRecordEntity(
      id: id,
      expertId: expertId,
      expertCodeName: expertCodeName,
      messageType: IrxMessageType.user,
      content: content,
      timestamp: timestamp ?? DateTime.now(),
      isRead: true, // 用户消息默认已读
      metadata: metadata,
    );
  }

  // 工厂方法：创建AI回复
  factory IrxChatRecordEntity.aiMessage({
    required String id,
    required String expertId,
    required String expertCodeName,
    required String content,
    DateTime? timestamp,
    Map<String, dynamic>? metadata,
  }) {
    return IrxChatRecordEntity(
      id: id,
      expertId: expertId,
      expertCodeName: expertCodeName,
      messageType: IrxMessageType.ai,
      content: content,
      timestamp: timestamp ?? DateTime.now(),
      isRead: false, // AI消息默认未读
      metadata: metadata,
    );
  }

  // 工厂方法：创建系统消息
  factory IrxChatRecordEntity.systemMessage({
    required String id,
    required String expertId,
    required String expertCodeName,
    required String content,
    DateTime? timestamp,
    Map<String, dynamic>? metadata,
  }) {
    return IrxChatRecordEntity(
      id: id,
      expertId: expertId,
      expertCodeName: expertCodeName,
      messageType: IrxMessageType.system,
      content: content,
      timestamp: timestamp ?? DateTime.now(),
      isRead: true, // 系统消息默认已读
      metadata: metadata,
    );
  }

  // 检查是否为用户消息
  bool get isUserMessage => messageType == IrxMessageType.user;
  
  // 检查是否为AI消息
  bool get isAiMessage => messageType == IrxMessageType.ai;
  
  // 检查是否为系统消息
  bool get isSystemMessage => messageType == IrxMessageType.system;

  // 获取格式化时间
  String get formattedTime {
    final now = DateTime.now();
    final difference = now.difference(timestamp);
    
    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}h ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d ago';
    } else {
      return '${timestamp.month}/${timestamp.day}';
    }
  }

  // 复制方法
  IrxChatRecordEntity copyWith({
    String? id,
    String? expertId,
    String? expertCodeName,
    IrxMessageType? messageType,
    String? content,
    DateTime? timestamp,
    bool? isRead,
    Map<String, dynamic>? metadata,
  }) {
    return IrxChatRecordEntity(
      id: id ?? this.id,
      expertId: expertId ?? this.expertId,
      expertCodeName: expertCodeName ?? this.expertCodeName,
      messageType: messageType ?? this.messageType,
      content: content ?? this.content,
      timestamp: timestamp ?? this.timestamp,
      isRead: isRead ?? this.isRead,
      metadata: metadata ?? this.metadata,
    );
  }

  // JSON序列化
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'expertId': expertId,
      'expertCodeName': expertCodeName,
      'messageType': messageType.name,
      'content': content,
      'timestamp': timestamp.toIso8601String(),
      'isRead': isRead,
      'metadata': metadata,
    };
  }

  // JSON反序列化
  factory IrxChatRecordEntity.fromJson(Map<String, dynamic> json) {
    return IrxChatRecordEntity(
      id: json['id'] as String,
      expertId: json['expertId'] as String,
      expertCodeName: json['expertCodeName'] as String,
      messageType: IrxMessageType.values.firstWhere(
        (e) => e.name == json['messageType'],
        orElse: () => IrxMessageType.user,
      ),
      content: json['content'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
      isRead: json['isRead'] as bool? ?? false,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  // 数据库映射
  Map<String, dynamic> toDbMap() {
    return {
      'id': id,
      'expert_id': expertId,
      'expert_code_name': expertCodeName,
      'message_type': messageType.name,
      'content': content,
      'timestamp': timestamp.millisecondsSinceEpoch,
      'is_read': isRead ? 1 : 0,
      'metadata': metadata != null ? 
          metadata.toString() : null, // 简单序列化，实际项目中可用JSON
    };
  }

  // 从数据库映射创建
  factory IrxChatRecordEntity.fromDbMap(Map<String, dynamic> map) {
    return IrxChatRecordEntity(
      id: map['id'] as String,
      expertId: map['expert_id'] as String,
      expertCodeName: map['expert_code_name'] as String,
      messageType: IrxMessageType.values.firstWhere(
        (e) => e.name == map['message_type'],
        orElse: () => IrxMessageType.user,
      ),
      content: map['content'] as String,
      timestamp: DateTime.fromMillisecondsSinceEpoch(map['timestamp'] as int),
      isRead: (map['is_read'] as int) == 1,
      metadata: null, // 简化处理
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is IrxChatRecordEntity &&
          runtimeType == other.runtimeType &&
          id == other.id &&
          expertId == other.expertId &&
          messageType == other.messageType &&
          content == other.content;

  @override
  int get hashCode =>
      id.hashCode ^
      expertId.hashCode ^
      messageType.hashCode ^
      content.hashCode;

  @override
  String toString() {
    return 'IrxChatRecordEntity{id: $id, expertCodeName: $expertCodeName, messageType: $messageType, content: ${content.length > 50 ? content.substring(0, 50) + '...' : content}}';
  }
}
