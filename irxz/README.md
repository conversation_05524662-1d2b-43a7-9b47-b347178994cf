# Irxz - 家庭衣物熨烫简易指导师

## 项目概述

Irxz是一款专业的AI驱动家庭衣物熨烫指导工具应用，通过10位专业AI熨烫师角色为用户提供个性化的衣物护理建议。应用结合智能对话系统和实用的迷你待办清单功能，帮助用户轻松掌握各种面料的熨烫技巧，让家庭衣物护理变得简单高效。

## 目标用户

- 家庭主妇/主夫：需要专业熨烫指导的日常衣物护理者
- 年轻上班族：希望快速学习衣物护理技巧的职场人士
- 服装爱好者：对不同面料护理有高要求的时尚人群
- 新手用户：缺乏熨烫经验但希望保护衣物的初学者

## 核心功能

### 1. AI角色对话系统
- **10位专业AI熨烫师**：每位角色专精不同面料和熨烫场景
- **智能对话引擎**：基于Moonshot文本生成模型，提供专业指导
- **打字机效果**：逐字显示AI回复，增强沉浸感
- **免费次数限制**：每个角色提供3次免费对话机会

### 2. 迷你待办清单
- **快速添加**：一键添加熨烫任务和提醒事项
- **即时完成**：点击任务显示划线完成效果
- **本地存储**：数据安全存储在设备本地
- **轻量化设计**：无复杂分类，专注简单实用

### 3. 聊天记录管理
- **历史查看**：完整保存所有对话记录
- **角色管理**：按AI角色分类管理聊天历史
- **灵活删除**：支持单条或批量删除记录

## AI角色专家团队

| 角色名称 | 英文代号 | 专业领域 | 核心技能 |
|---------|---------|---------|---------|
| 棉质衣物熨烫师 | CottonIron | 棉质面料护理 | 中高温熨烫技巧，蒸汽控制 |
| 羊毛衣物熨烫师 | WoolIron | 羊毛面料护理 | 低温保护，防变形技巧 |
| 丝绸衣物熨烫师 | SilkIron | 丝质面料护理 | 超低温熨烫，防损伤技术 |
| 化纤衣物熨烫师 | SyntheticIron | 化纤面料护理 | 防融化技巧，快速熨烫 |
| 衬衫快速熨烫师 | ShirtQuickIron | 衬衫专业护理 | 高效熨烫流程，时间管理 |
| 裤子熨烫师 | PantsIron | 裤装护理 | 折痕处理，形状保持 |
| 蒸汽挂烫师 | SteamerIron | 挂烫技术 | 蒸汽设备使用，距离控制 |
| 褶皱应急熨烫师 | WrinkleFix | 应急处理 | 快速除皱，旅行技巧 |
| 熨烫工具使用师 | IronTool | 工具使用 | 设备操作，配件应用 |
| 免熨技巧指导师 | NoIronTips | 预防护理 | 免熨技巧，日常保养 |

## 技术架构

### 状态管理 + 设计模式
- **Redux + MVVM**：确保数据流清晰，状态管理高效
- **本地数据库**：SQLite存储聊天记录和用户数据
- **AI引擎**：Moonshot文本生成模型驱动对话系统

### 核心目录架构（熨烫场景化命名）

#### 🔥 核心架构目录
1. **FabricVault** (数据实体层)
   - 替代传统"Models"，存储面料档案和熨烫参数
   - TextileFolio：面料耐温阈值数据
   - PressBlueprint：熨烫方案库

2. **LoomAtelier** (界面交互层)
   - 替代传统"Screens"，熨烫操作界面空间
   - IronLoom：实时温度调节界面
   - SmoothGallery：效果展示区

3. **IronLogic** (业务协调层-MVVM)
   - 替代传统"ViewModels"，管理状态到视图转换
   - HeatAdapter：温度适配器
   - SteamMediator：蒸汽协调器

4. **SteamCore** (状态管理层-Redux)
   - 替代传统"Store"，管理全局状态流
   - PressActions：操作动作定义
   - HeatReducer：状态转换逻辑
   - FabricState：当前状态数据

#### 🌡️ 扩展功能目录
5. **FabricSage** (AI指导引擎)
6. **CottonSquare** (社交协作层)
7. **StarchArchive** (知识库)
8. **WeaveParts** (UI组件库)
9. **ThreadRoutes** (配置与路由)

## 设计系统

### 🎯 核心配色方案
- **主品牌色**：#3498DB (智能蒸汽蓝)
- **辅助强调色**：#1ABC9C (融滑青绿)
- **提示反馈色**：#F1C40F (热力金黄)、#E74C3C (错误警告红)
- **基础中性色**：#FFFFFF (背景白)、#F5F5F5 (浅灰)、#2C3E50 (深炭灰)、#7F8C8D (中灰)

### 🎨 角色专属配色
- AI角色聊天气泡采用淡紫渐变色调，体现不同织物的柔和质感
- SilkIron：#7D3C98 (丝质高雅)
- CottonIron：#A569BD (棉质柔软)

## 页面结构规划

| 页面名称 | 功能描述 | 主要特性 |
|---------|---------|---------|
| 首页角色选择 | 展示10个AI角色，支持标签分类 | 角色卡片展示，快速进入对话 |
| AI对话页面 | 与选定角色进行专业对话 | 打字机效果，次数显示，专业指导 |
| 迷你待办清单 | 管理熨烫任务和提醒事项 | 快速添加，一键完成，本地存储 |
| 聊天历史记录 | 查看和管理历史对话 | 按角色分类，支持删除操作 |
| 个人中心 | 用户信息和偏好设置 | 头像昵称，收藏角色，反馈功能 |

## 开发状态跟踪

| 页面/组件名称 | 开发状态 | 文件路径 | 备注 |
|-------------|---------|---------|------|
| 项目初始化 | ✅ 完成 | - | Flutter项目基础结构已建立 |
| 核心架构设计 | 📋 规划中 | - | Redux+MVVM架构设计 |
| 首页角色选择 | ⏳ 待开发 | - | 10个AI角色展示界面 |
| AI对话系统 | ⏳ 待开发 | - | 聊天界面和AI集成 |
| 待办清单功能 | ⏳ 待开发 | - | 迷你任务管理系统 |
| 聊天历史管理 | ⏳ 待开发 | - | 历史记录查看和管理 |
| 个人中心 | ⏳ 待开发 | - | 用户信息和设置 |

## 技术实现细节

*此部分将随着开发过程逐步添加各页面的具体技术方案*

---

**开发环境**: Flutter 3.5.4+ | iOS平台优先 | Redux状态管理 | SQLite本地存储
