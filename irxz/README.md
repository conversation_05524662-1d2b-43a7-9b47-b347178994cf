# Irxz - 家庭衣物熨烫简易指导师

## 项目概述

Irxz是一款专业的AI驱动家庭衣物熨烫指导工具应用，通过10位专业AI熨烫师角色为用户提供个性化的衣物护理建议。应用结合智能对话系统和实用的迷你待办清单功能，帮助用户轻松掌握各种面料的熨烫技巧，让家庭衣物护理变得简单高效。

## 目标用户

- 家庭主妇/主夫：需要专业熨烫指导的日常衣物护理者
- 年轻上班族：希望快速学习衣物护理技巧的职场人士
- 服装爱好者：对不同面料护理有高要求的时尚人群
- 新手用户：缺乏熨烫经验但希望保护衣物的初学者

## 核心功能

### 1. AI角色对话系统
- **10位专业AI熨烫师**：每位角色专精不同面料和熨烫场景
- **智能对话引擎**：基于Moonshot文本生成模型，提供专业指导
- **打字机效果**：逐字显示AI回复，增强沉浸感
- **免费次数限制**：每个角色提供3次免费对话机会

### 2. 迷你待办清单
- **快速添加**：一键添加熨烫任务和提醒事项
- **即时完成**：点击任务显示划线完成效果
- **本地存储**：数据安全存储在设备本地
- **轻量化设计**：无复杂分类，专注简单实用

### 3. 聊天记录管理
- **历史查看**：完整保存所有对话记录
- **角色管理**：按AI角色分类管理聊天历史
- **灵活删除**：支持单条或批量删除记录

## AI角色专家团队

| 角色名称 | 英文代号 | 专业领域 | 核心技能 |
|---------|---------|---------|---------|
| 棉质衣物熨烫师 | CottonIron | 棉质面料护理 | 中高温熨烫技巧，蒸汽控制 |
| 羊毛衣物熨烫师 | WoolIron | 羊毛面料护理 | 低温保护，防变形技巧 |
| 丝绸衣物熨烫师 | SilkIron | 丝质面料护理 | 超低温熨烫，防损伤技术 |
| 化纤衣物熨烫师 | SyntheticIron | 化纤面料护理 | 防融化技巧，快速熨烫 |
| 衬衫快速熨烫师 | ShirtQuickIron | 衬衫专业护理 | 高效熨烫流程，时间管理 |
| 裤子熨烫师 | PantsIron | 裤装护理 | 折痕处理，形状保持 |
| 蒸汽挂烫师 | SteamerIron | 挂烫技术 | 蒸汽设备使用，距离控制 |
| 褶皱应急熨烫师 | WrinkleFix | 应急处理 | 快速除皱，旅行技巧 |
| 熨烫工具使用师 | IronTool | 工具使用 | 设备操作，配件应用 |
| 免熨技巧指导师 | NoIronTips | 预防护理 | 免熨技巧，日常保养 |

## 技术架构

### 状态管理 + 设计模式
- **Redux + MVVM**：确保数据流清晰，状态管理高效
- **本地数据库**：SQLite存储聊天记录和用户数据
- **AI引擎**：Moonshot文本生成模型驱动对话系统

### 核心目录架构（熨烫场景化命名）

#### 🔥 核心架构目录
1. **FabricVault** (数据实体层)
   - 替代传统"Models"，存储面料档案和熨烫参数
   - TextileFolio：面料耐温阈值数据
   - PressBlueprint：熨烫方案库

2. **LoomAtelier** (界面交互层)
   - 替代传统"Screens"，熨烫操作界面空间
   - IronLoom：实时温度调节界面
   - SmoothGallery：效果展示区

3. **IronLogic** (业务协调层-MVVM)
   - 替代传统"ViewModels"，管理状态到视图转换
   - HeatAdapter：温度适配器
   - SteamMediator：蒸汽协调器

4. **SteamCore** (状态管理层-Redux)
   - 替代传统"Store"，管理全局状态流
   - PressActions：操作动作定义
   - HeatReducer：状态转换逻辑
   - FabricState：当前状态数据

#### 🌡️ 扩展功能目录
5. **FabricSage** (AI指导引擎)
6. **CottonSquare** (社交协作层)
7. **StarchArchive** (知识库)
8. **WeaveParts** (UI组件库)
9. **ThreadRoutes** (配置与路由)

## 设计系统

### 🎯 核心配色方案
- **主品牌色**：#3498DB (智能蒸汽蓝)
- **辅助强调色**：#1ABC9C (融滑青绿)
- **提示反馈色**：#F1C40F (热力金黄)、#E74C3C (错误警告红)
- **基础中性色**：#FFFFFF (背景白)、#F5F5F5 (浅灰)、#2C3E50 (深炭灰)、#7F8C8D (中灰)

### 🎨 角色专属配色
- AI角色聊天气泡采用淡紫渐变色调，体现不同织物的柔和质感
- SilkIron：#7D3C98 (丝质高雅)
- CottonIron：#A569BD (棉质柔软)

## 页面结构规划

| 页面名称 | 功能描述 | 主要特性 |
|---------|---------|---------|
| 首页角色选择 | 展示10个AI角色，支持标签分类 | 角色卡片展示，快速进入对话 |
| AI对话页面 | 与选定角色进行专业对话 | 打字机效果，次数显示，专业指导 |
| 迷你待办清单 | 管理熨烫任务和提醒事项 | 快速添加，一键完成，本地存储 |
| 聊天历史记录 | 查看和管理历史对话 | 按角色分类，支持删除操作 |
| 个人中心 | 用户信息和偏好设置 | 头像昵称，收藏角色，反馈功能 |

## 开发状态跟踪

| 页面/组件名称 | 开发状态 | 文件路径 | 备注 |
|-------------|---------|---------|------|
| 项目初始化 | ✅ 完成 | - | Flutter项目基础结构已建立 |
| 依赖包配置 | ✅ 完成 | pubspec.yaml | Redux、动画、数据库等依赖已配置 |
| 资源文件下载 | ✅ 完成 | assets/Irx/ | AI角色图片和用户头像已下载 |
| Redux状态管理 | ✅ 完成 | lib/SteamCore/ | 状态、Actions、Reducer、Store已实现 |
| 数据实体模型 | ✅ 完成 | lib/FabricVault/ | AI专家、聊天、任务、用户实体已定义 |
| 核心架构设计 | ✅ 完成 | - | Redux+MVVM架构已实现 |
| 首页角色选择 | ⏳ 待开发 | lib/LoomAtelier/ | 10个AI角色展示界面 |
| AI对话系统 | ⏳ 待开发 | lib/LoomAtelier/ | 聊天界面和Moonshot AI集成 |
| 待办清单功能 | ⏳ 待开发 | lib/LoomAtelier/ | 迷你任务管理系统 |
| 聊天历史管理 | ⏳ 待开发 | lib/LoomAtelier/ | 历史记录查看和管理 |
| 个人中心 | ⏳ 待开发 | lib/LoomAtelier/ | 用户信息和设置 |

## 开发规范与要求

### 🚨 核心开发规则（严格遵循）

#### 1. 命名差异化规范
**禁用通用词汇**：
- ❌ 禁用：home, history, profile, bottom, navigation, feedback, shop, store, purchase, buy, post, detail, coin, user
- ✅ 替代方案：使用英文表达变体、拼音、首字母缩写或创意命名

**文件命名规则**：
- 统一前缀：`Irx` (来自产品名Irxz)
- 示例：`IrxMainHub.dart`, `IrxChatFlow.dart`, `IrxTaskMgr.dart`

**变量/类名混乱化策略**：
- 截断规则：保留前4-6字母 + 随机数字
- 数字插入：单词中间插入1-100随机数
- 拼音替换：部分英文替换为中文拼音缩写
- 词汇压缩：删除部分字母但保持可识别性

#### 2. 国际化要求
- 🌍 **纯英文界面**：所有用户可见文本必须为英文
- 📝 **中文注释**：代码注释和控制台输出可使用中文
- ⚠️ **权限描述**：iOS权限设置描述必须为英文（审核要求）

#### 3. 技术架构要求
- **状态管理**：Redux + MVVM设计模式
- **动画系统**：Lottie、Flutter Animate、Simple Animations（禁用低端Canvas动画）
- **本地存储**：SQLite数据库
- **AI集成**：Moonshot API聊天引擎

#### 4. 资源管理规范
- **图片资源**：统一存放在`/assets/Irx/`目录
- **角色头像**：已提供irx_1.png到irx_10.png
- **字体资源**：根据主题需求联网搜索并本地化
- **用户头像**：通过Unsplash API动态获取

### 🎨 UI设计要求

#### 主页设计规范
- **卡片样式**：个性化信息流，非常规设计
- **图片比例**：竖屏16:9大图展示角色信息
- **交互效果**：高级动画过渡，避免粗糙效果

#### 视觉层次系统
- **圆角系统**：卡片16px，按钮12px，输入框8px，小元素6px
- **阴影系统**：轻微(8px blur)，中等(12px blur)，强烈(16px blur)
- **间距系统**：基础8px，组件16px，页面20px，内容12px

### 🔌 API集成配置

#### Moonshot AI聊天API
```bash
curl https://api.moonshot.cn/v1/chat/completions \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer sk-UjLDXgyVsdh4kNtrLbbu1yFFhuafggJTxEw03ezpnnvXX2fR"
```

#### Unsplash图片API
- **Application ID**: 767748
- **Access Key**: sB7G-rHWgjtvcp1Ag78GATMnmDApT_WACkbWdygsgKM
- **Secret Key**: b-RsiPe7hsfsVDneyWel1503jBiaKFBMqCq5dUTWvAY

### 📁 项目文件结构预览

```
irxz/
├── lib/
│   ├── FabricVault/          # 数据实体层
│   ├── LoomAtelier/          # 界面交互层
│   ├── IronLogic/            # 业务协调层(MVVM)
│   ├── SteamCore/            # 状态管理层(Redux)
│   ├── FabricSage/           # AI指导引擎
│   ├── WeaveParts/           # UI组件库
│   └── ThreadRoutes/         # 配置与路由
├── assets/
│   └── Irx/                  # 项目资源文件
│       ├── irx_1.png ~ irx_10.png  # AI角色头像
│       ├── fonts/            # 自定义字体
│       └── animations/       # Lottie动画文件
└── scripts/
    └── unsplash_downloader.py # 图片下载脚本
```

## 技术实现细节

*此部分将随着开发过程逐步添加各页面的具体技术方案*

---

**开发环境**: Flutter 3.5.4+ | iOS平台优先 | Redux+MVVM架构 | SQLite本地存储 | Moonshot AI集成
