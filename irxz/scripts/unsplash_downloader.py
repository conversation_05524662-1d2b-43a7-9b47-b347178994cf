#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Unsplash图片下载脚本
用于下载用户头像和其他项目所需图片资源
"""

import requests
import os
import json
from typing import List, Dict, Optional
import time

class UnsplashDownloader:
    def __init__(self):
        self.access_key = "sB7G-rHWgjtvcp1Ag78GATMnmDApT_WACkbWdygsgKM"
        self.secret_key = "b-RsiPe7hsfsVDneyWel1503jBiaKFBMqCq5dUTWvAY"
        self.app_id = "767748"
        self.base_url = "https://api.unsplash.com"
        self.headers = {
            "Authorization": f"Client-ID {self.access_key}",
            "Accept-Version": "v1"
        }
        
        # 确保下载目录存在
        self.download_dir = "../assets/Irx/avatars"
        os.makedirs(self.download_dir, exist_ok=True)
    
    def search_photos(self, query: str, per_page: int = 30, page: int = 1) -> Optional[List[Dict]]:
        """
        搜索图片
        
        Args:
            query: 搜索关键词
            per_page: 每页图片数量
            page: 页码
            
        Returns:
            图片信息列表
        """
        url = f"{self.base_url}/search/photos"
        params = {
            "query": query,
            "per_page": per_page,
            "page": page,
            "orientation": "portrait"  # 竖向图片，适合头像
        }
        
        try:
            response = requests.get(url, headers=self.headers, params=params)
            response.raise_for_status()
            data = response.json()
            return data.get("results", [])
        except requests.RequestException as e:
            print(f"搜索图片失败: {e}")
            return None
    
    def download_image(self, image_url: str, filename: str) -> bool:
        """
        下载单张图片
        
        Args:
            image_url: 图片URL
            filename: 保存的文件名
            
        Returns:
            下载是否成功
        """
        try:
            response = requests.get(image_url, stream=True)
            response.raise_for_status()
            
            filepath = os.path.join(self.download_dir, filename)
            with open(filepath, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            print(f"✅ 下载成功: {filename}")
            return True
        except requests.RequestException as e:
            print(f"❌ 下载失败 {filename}: {e}")
            return False
    
    def download_avatars(self, keywords: List[str], count_per_keyword: int = 5):
        """
        批量下载头像图片
        
        Args:
            keywords: 搜索关键词列表
            count_per_keyword: 每个关键词下载的图片数量
        """
        print("🚀 开始下载用户头像图片...")
        
        total_downloaded = 0
        
        for keyword in keywords:
            print(f"\n🔍 搜索关键词: {keyword}")
            photos = self.search_photos(keyword, per_page=count_per_keyword)
            
            if not photos:
                print(f"⚠️ 未找到关键词 '{keyword}' 的图片")
                continue
            
            for i, photo in enumerate(photos[:count_per_keyword]):
                # 获取中等尺寸的图片URL
                image_url = photo["urls"]["regular"]
                filename = f"avatar_{keyword}_{i+1}_{photo['id']}.jpg"
                
                if self.download_image(image_url, filename):
                    total_downloaded += 1
                
                # 避免请求过于频繁
                time.sleep(0.5)
        
        print(f"\n🎉 下载完成！总共下载了 {total_downloaded} 张头像图片")
    
    def download_theme_images(self, theme_keywords: List[str], count: int = 3):
        """
        下载主题相关图片
        
        Args:
            theme_keywords: 主题关键词列表
            count: 每个主题下载的图片数量
        """
        print("🎨 开始下载主题图片...")
        
        for keyword in theme_keywords:
            print(f"\n🔍 搜索主题: {keyword}")
            photos = self.search_photos(keyword, per_page=count)
            
            if not photos:
                continue
            
            for i, photo in enumerate(photos[:count]):
                image_url = photo["urls"]["regular"]
                filename = f"theme_{keyword}_{i+1}_{photo['id']}.jpg"
                self.download_image(image_url, filename)
                time.sleep(0.5)

def main():
    """主函数"""
    downloader = UnsplashDownloader()
    
    # 用户头像关键词（多样化的人物头像）
    avatar_keywords = [
        "portrait", "face", "person", "professional", "business",
        "casual", "smile", "avatar", "headshot", "profile"
    ]
    
    # 熨烫主题相关关键词
    theme_keywords = [
        "iron", "fabric", "textile", "clothing", "laundry",
        "steam", "cotton", "silk", "wool", "care"
    ]
    
    print("=" * 50)
    print("Irxz 项目图片资源下载器")
    print("=" * 50)
    
    # 下载用户头像
    downloader.download_avatars(avatar_keywords, count_per_keyword=3)
    
    # 下载主题图片
    downloader.download_theme_images(theme_keywords, count=2)
    
    print("\n✨ 所有图片下载完成！")
    print(f"📁 图片保存位置: {downloader.download_dir}")

if __name__ == "__main__":
    main()
